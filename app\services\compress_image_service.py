# services/compression.py
import time
from io import BytesIO
from typing import Dict, <PERSON><PERSON>

from PIL import Image, ImageOps

try:
    import pillow_heif  # enables HEIC/HEIF reading via Pillow
    pillow_heif.register_heif_opener()
except Exception:
    pass

MAX_BYTES = 5 * 1024 * 1024  # 5MB Rekognition Bytes limit
MAX_SIDE = 1600              # longest side target

def _open_image_any(bytes_in: bytes) -> Image.Image:
    im = Image.open(BytesIO(bytes_in))
    im.load()
    return im

def _format_size(num_bytes: int) -> str:
    """Return human-readable file size (B, KB, or MB)."""
    if num_bytes < 1024:
        return f"{num_bytes} B"
    elif num_bytes < 1024 * 1024:
        return f"{num_bytes / 1024:.1f} KB"
    else:
        return f"{num_bytes / (1024 * 1024):.2f} MB"

def _normalize_and_resize(im: Image.Image) -> Image.Image:
    im = ImageOps.exif_transpose(im)
    if im.mode not in ("RGB", "L"):
        im = im.convert("RGB")
    w, h = im.size
    if max(w, h) > MAX_SIDE:
        scale = MAX_SIDE / float(max(w, h))
        im = im.resize((int(w * scale), int(h * scale)), resample=Image.LANCZOS)
    return im

def _encode_jpeg_under_limit(im: Image.Image, target_bytes: int) -> Tuple[bytes, int]:
    # Returns (jpeg_bytes, quality_used)
    for q in (85, 80, 75, 70, 65, 60):
        buf = BytesIO()
        im.save(buf, format="JPEG", quality=q, optimize=True, progressive=True)
        data = buf.getvalue()
        if len(data) <= target_bytes:
            return data, q
    # Last-chance resize + save
    w, h = im.size
    im2 = im.resize((int(w * 0.85), int(h * 0.85)), resample=Image.LANCZOS)
    buf = BytesIO()
    im2.save(buf, format="JPEG", quality=60, optimize=True, progressive=True)
    return buf.getvalue(), 60

def compress_image_for_rekognition(image_bytes: bytes) -> Dict:
    """
    Returns a dict with:
      - processed_bytes: bytes
      - content_type: 'image/jpeg'
      - metrics: {original_bytes, processed_bytes, reduction_pct, original_size, processed_size, quality_used, elapsed_ms}
    """
    t0 = time.perf_counter()

    original_size_bytes = len(image_bytes)
    im = _open_image_any(image_bytes)
    orig_w, orig_h = im.size

    im = _normalize_and_resize(im)
    resized_w, resized_h = im.size

    jpeg_bytes, q_used = _encode_jpeg_under_limit(im, MAX_BYTES)

    elapsed_ms = int((time.perf_counter() - t0) * 1000)
    processed_size_bytes = len(jpeg_bytes)

    reduction_pct = 0.0
    if original_size_bytes > 0:
        reduction_pct = round(100.0 * (original_size_bytes - processed_size_bytes) / original_size_bytes, 2)

    return {
        "processed_bytes": jpeg_bytes,
        "content_type": "image/jpeg",
        "metrics": {
            "original_bytes": _format_size(original_size_bytes),
            "processed_bytes": _format_size(processed_size_bytes),
            "reduction_pct": reduction_pct,
            "original_size": {"width": orig_w, "height": orig_h},
            "processed_size": {"width": resized_w, "height": resized_h},
            "quality_used": q_used,
            "elapsed_ms": elapsed_ms,
        },
    }
