# Git
.git
.gitignore
README.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/
.env.sample
# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
temp/
*.log
.env.local
.env.production
.env
Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
docs/
*.md

# Tests
tests/
test_*
*_test.py

# Development tools
.flake8
.isort.cfg
.black
mypy.ini
pytest.ini
setup.cfg
tox.ini

# Build artifacts
build/
dist/
*.egg-info/

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints

# Data files
*.csv
*.json
*.xml
*.sql

# Backup files
*.bak
*.backup
*.old

# Temporary files
tmp/
temp/
*.tmp

.pre-commit-config.yaml
.editorconfig
ecs/*
.github/*
ecs/
.github/