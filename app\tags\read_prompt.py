import hashlib
import os
import tempfile
import time
from pathlib import Path

import requests

# ---- CONFIG ----
DOC_ID = "1w4swtf9oYMlD5ZXW6_obympiB4h-SHqDbLAUR8S6WQo"  # <- your doc id
LOCAL_PROMPT_FILE = os.getenv("LOCAL_PROMPT_FILE", "prompt.txt")
REFRESH_INTERVAL = int(os.getenv("PROMPT_REFRESH_INTERVAL", "60"))  # seconds

# cross-platform temp dir
TMP_DIR = Path(os.getenv("PROMPT_CACHE_DIR", tempfile.gettempdir())) / "prompt_cache"
TMP_DIR.mkdir(parents=True, exist_ok=True)
CACHE_PATH = TMP_DIR / "prompt_cache.txt"
META_PATH = TMP_DIR / "prompt_meta.txt"

def _hash(text: str) -> str:
    return hashlib.md5(text.encode("utf-8")).hexdigest()

def _fetch_from_google(doc_id: str, etag: str | None = None):
    url = f"https://docs.google.com/document/d/{doc_id}/export?format=txt"
    headers = {"If-None-Match": etag} if etag else {}
    resp = requests.get(url, headers=headers, timeout=10)
    return resp

def read_prompt(force_refresh: bool = False) -> str:
    now = time.time()
    last_check = 0.0
    etag: str | None = None

    if META_PATH.exists():
        try:
            meta = META_PATH.read_text(encoding="utf-8").strip().split("|")
            etag = meta[0] if meta else None
            last_check = float(meta[1]) if len(meta) > 1 else 0.0
        except Exception:
            pass

    # fresh cache window
    if not force_refresh and (now - last_check < REFRESH_INTERVAL) and CACHE_PATH.exists():
        print("⚡ Using cached prompt (fresh).")
        return CACHE_PATH.read_text(encoding="utf-8")

    try:
        if DOC_ID:
            resp = _fetch_from_google(DOC_ID, etag)
            if resp.status_code == 304 and CACHE_PATH.exists():
                print("✅ Prompt not modified (304).")
                return CACHE_PATH.read_text(encoding="utf-8")
            if resp.status_code == 200:
                text = resp.text
                new_etag = resp.headers.get("ETag", _hash(text))
                CACHE_PATH.write_text(text, encoding="utf-8")
                META_PATH.write_text(f"{new_etag}|{now}", encoding="utf-8")
                print("🔄 Prompt updated from Google Doc.")
                return text
            # unexpected status -> try cache
            print(f"⚠️ Unexpected HTTP {resp.status_code}; falling back to cache.")
            if CACHE_PATH.exists():
                return CACHE_PATH.read_text(encoding="utf-8")
            return ""
        else:
            # local fallback
            print("📄 Using local prompt file.")
            text = Path(LOCAL_PROMPT_FILE).read_text(encoding="utf-8")
            CACHE_PATH.write_text(text, encoding="utf-8")
            META_PATH.write_text(f"{_hash(text)}|{now}", encoding="utf-8")
            return text

    except Exception as e:
        print(f"⚠️ Error fetching prompt: {e}")
        if CACHE_PATH.exists():
            print("💾 Fallback to cached prompt.")
            return CACHE_PATH.read_text(encoding="utf-8")
        return ""
