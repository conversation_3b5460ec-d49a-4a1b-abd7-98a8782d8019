"""User model for DynamoDB."""

from typing import Any

from pydantic import BaseModel


class User(BaseModel):
    """User model for DynamoDB."""

    user_id: str
    unique_id: str | None = None
    email: str | None = None
    created_at: str
    last_login: str
    status: str = "active"

    @property
    def pk(self) -> str:
        """Primary key for DynamoDB."""
        return f"USER#{self.user_id}"

    @property
    def sk(self) -> str:
        """Sort key for DynamoDB."""
        return "PROFILE"

    def to_dynamodb_item(self) -> dict[str, Any]:
        """Convert to DynamoDB item format."""
        item = {
            "PK": self.pk,
            "SK": self.sk,
            "user_id": self.user_id,
            "created_at": self.created_at,
            "last_login": self.last_login,
            "status": self.status,
        }

        # Add unique_id if present
        if self.unique_id:
            item["unique_id"] = self.unique_id

        # Add email if present
        if self.email:
            item["email"] = self.email
            # Add GSI for email lookup
            item["GSI1PK"] = f"EMAIL#{self.email}"

        return item

    @classmethod
    def from_dynamodb_item(cls, item: dict[str, Any]) -> "User":
        """Create User from DynamoDB item."""
        return cls(
            user_id=item["user_id"],
            unique_id=item.get("unique_id"),
            email=item.get("email"),
            created_at=item["created_at"],
            last_login=item["last_login"],
            status=item.get("status", "active"),
        )
