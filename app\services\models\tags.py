"""Pydantic models for tags service."""

from typing import List, Optional

from pydantic import BaseModel, Field


class TagCategory(BaseModel):
    """Model for tag category."""
    parent_cat: str = Field(..., description="Parent category name")
    tags: List[str] = Field(..., description="List of tags in this category")


class ImageTags(BaseModel):
    """Model for image tags."""
    image_id: str = Field(..., description="S3 key of the image")
    tags: List[TagCategory] = Field(..., description="Tags grouped by category")
    suggested_tags: List[str] = Field(..., description="List of suggested_tags in this category")


class UserTagsResponse(BaseModel):
    """Response model for user tags."""
    user_id: str = Field(..., description="User ID")
    labels: List[ImageTags] = Field(..., description="Image tags for the user")


class ImageTagUpdate(BaseModel):
    """Model for updating image tags."""
    image_id: str = Field(..., description="Image ID to update")
    tags: List[str] = Field(..., description="List of tag names to set for this image")


# For direct list input, we'll use List[ImageTagUpdate] directly in the route


class MasterTag(BaseModel):
    """Model for master tag."""
    marriott_tag: str = Field(..., description="Marriott tag name")
    activity_image: Optional[str] = Field(None, description="Activity image filename")


class MasterTagCategory(BaseModel):
    """Model for master tag category."""
    parent_cat: str = Field(..., description="Parent category name")
    parent_image: Optional[str] = Field(None, description="Parent category image filename")
    parent_desc: Optional[str] = Field(None, description="Parent category description")
    tags: List[MasterTag] = Field(..., description="List of tags in this category")


class AllMasterTagsResponse(BaseModel):
    """Response model for all master tags."""
    categories: List[MasterTagCategory] = Field(..., description="All master tag categories")
