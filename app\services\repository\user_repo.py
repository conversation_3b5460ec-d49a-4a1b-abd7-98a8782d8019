"""User service for database operations."""

import uuid
from datetime import UTC, datetime

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

from app.core.database import get_db_service
from app.core.sentry import capture_exception
from app.services.models.user import User


class UserService:
    """Service for user-related database operations."""

    def __init__(self) -> None:
        """Initialize user service."""
        self.db = get_db_service()
        self.table = self.db.table

    async def get_user_by_unique_id(self, unique_id: str) -> User | None:
        """Get user by unique_id (device_id)."""
        try:
            # Scan for user with unique_id (handle both spellings)
            # response = self.table.scan(
            #     FilterExpression="attribute_exists(unique_id) AND unique_id = :uid OR attribute_exists(unqiue_id) AND unqiue_id = :uid",
            #     ExpressionAttributeValues={":uid": unique_id},
            # )
            
            response = self.table.query(
            IndexName="unique_id_index",
            KeyConditionExpression=Key("unique_id").eq(unique_id)
        )


            if response["Items"]:
                item = response["Items"][0]
                return User(
                    user_id=item["user_id"],
                    unique_id=item.get("unique_id"),
                    email=item.get("email"),
                    created_at=item["created_at"],
                    last_login=item["last_login"],
                    status=item.get("status", "active"),
                )

            return None

        except ClientError as e:
            capture_exception(
                e,
                extra_data={"unique_id": unique_id},
            )
            return None

    async def get_user_by_email(self, email: str) -> User | None:
        """Get user by email."""
        try:
            # Scan for user with email (in production, use GSI)
            response = self.table.scan(
                FilterExpression="attribute_exists(email) AND email = :email",
                ExpressionAttributeValues={":email": email},
            )

            if response["Items"]:
                item = response["Items"][0]
                return User(
                    user_id=item["user_id"],
                    unique_id=item.get("unique_id"),
                    email=item.get("email"),
                    created_at=item["created_at"],
                    last_login=item["last_login"],
                    status=item.get("status", "active"),
                )

            return None

        except ClientError as e:
            capture_exception(
                e,
                extra_data={"email": email},
            )
            return None

    async def find_user_by_identifier(
        self, unique_id: str | None = None, email: str | None = None
    ) -> User | None:
        """Find user by unique_id or email."""
        if email:
            user = await self.get_user_by_email(email)
            if user:
                return user

        if unique_id:
            user = await self.get_user_by_unique_id(unique_id)
            if user:
                return user

        return None

    async def create_user(
        self, unique_id: str | None = None, email: str | None = None
    ) -> User:
        """Create new user."""
        now = datetime.now(UTC).isoformat()
        user_id = str(uuid.uuid4())

        user = User(
            user_id=user_id,
            unique_id=unique_id,
            email=email,
            created_at=now,
            last_login=now,
            status="active",
        )

        try:
            self.table.put_item(Item=user.to_dynamodb_item())
            return user

        except ClientError as e:
            raise Exception(f"Failed to create user: {e}")

    async def update_last_login(self, user_id: str) -> None:
        """Update user's last login timestamp."""
        now = datetime.now(UTC).isoformat()

        try:
            self.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": "PROFILE"},
                UpdateExpression="SET last_login = :timestamp",
                ExpressionAttributeValues={":timestamp": now},
            )

        except ClientError as e:
            capture_exception(e, extra_data={"user_id": user_id})

    async def has_recommendations(self, user_id: str) -> bool:
        """Check if user has any recommendations generated."""
        try:
            response = self.table.get_item(
                Key={
                    "PK": f"USER#{user_id}",
                    "SK": f"RECOMMENDATION#{user_id}"
                }
            )
            return "Item" in response
        except ClientError as e:
            capture_exception(e, extra_data={"user_id": user_id})
            return False


# Global service instance
_user_service: UserService | None = None


def get_user_service() -> UserService:
    """Get the global user service instance."""
    global _user_service
    if _user_service is None:
        _user_service = UserService()
    return _user_service
