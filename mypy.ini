[mypy]
python_version = 3.11
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
strict_concatenate = true
show_error_codes = true
show_column_numbers = true
pretty = true

# Specific module configurations
[mypy-app.*]
strict = true

[mypy-tests.*]
disallow_untyped_defs = false
strict = false

# Third-party libraries without stubs
[mypy-uvicorn.*]
ignore_missing_imports = true

[mypy-pytest.*]
ignore_missing_imports = true
