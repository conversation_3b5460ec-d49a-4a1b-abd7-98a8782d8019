#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Passion Matcher (v5b) — AWS Rekognition labels -> PASSIONS taxonomy
- Embeddings (Bedrock Titan v2) + cosine + tiny domain/rule nudges
- Returns BOTH:
  * "matched"   = high-confidence primary picks
  * "suggested" = softer, still-relevant ideas (vibe suggestions)
- Reads labels from JSON: { "img_id": [ {"label": "...", "score": 0.99}, ... ], ... }
- Imports PASSIONS from passions_list.py
- No master CSV required; uses only the labels you pass

Run:
  python match_passion_aws_v5b.py \
    --labels aws_labels.json \
    --topk 6 --minscore 0.30 --perparent 2 \
    --suggestk 6 --suggestminscore 0.25 --suggest_perparent 2
"""

import argparse
import hashlib
import json
import pathlib
import sys
import time
from typing import Any, Dict, List, Set, Tuple

import boto3
import numpy as np
from botocore.config import Config

from app.core.config import get_settings

# ============ YOUR AWS CREDS (quick local test only) ============
# Prefer ENV or ~/.aws/credentials; hardcode only for quick local runs.
settings = get_settings()
AWS_ACCESS_KEY_ID = settings.aws_access_key_id
AWS_SECRET_ACCESS_KEY = settings.aws_secret_access_key     # e.g. "abcd...."
AWS_SESSION_TOKEN = None         # if using temporary creds
AWS_REGION = "us-east-1"
EMBED_MODEL = "amazon.titan-embed-text-v2:0"  # Titan v2 text, ~1536 dims

# ---------- Bedrock Runtime client ----------
def _make_bedrock_client():
    kwargs = {
        "service_name": "bedrock-runtime",
        "region_name": AWS_REGION,
        "config": Config(retries={"max_attempts": 3, "mode": "standard"}),
    }
    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
        kwargs.update({
            "aws_access_key_id": AWS_ACCESS_KEY_ID,
            "aws_secret_access_key": AWS_SECRET_ACCESS_KEY,
        })
        if AWS_SESSION_TOKEN:
            kwargs["aws_session_token"] = AWS_SESSION_TOKEN
    return boto3.client(**kwargs)

brt = _make_bedrock_client()

# ============ PASSIONS (always import from your file) ============
try:
    from app.tags.passions_list import PASSIONS  # [{"tag":..., "parent":...}, ...]
except Exception as e:
    print("ERROR: Could not import PASSIONS from passions_list.py:", e)
    sys.exit(1)

# ============ CLI ============
def parse_args():
    ap = argparse.ArgumentParser(description="Passion matcher (AWS labels -> PASSIONS vibes)")
    ap.add_argument("--labels", default="aws_labels.json",
                    help="Path to JSON file with images and scored labels (default: aws_labels.json)")
    # primary matches
    ap.add_argument("--topk", type=int, default=6, help="Top-K matches per image (default: 6)")
    ap.add_argument("--minscore", type=float, default=0.30, help="Minimum blended score for matches (default: 0.30)")
    ap.add_argument("--perparent", type=int, default=2, help="Max matches per parent (default: 2)")
    # suggestions
    ap.add_argument("--suggestk", type=int, default=6, help="Top-K suggestions per image (default: 6)")
    ap.add_argument("--suggestminscore", type=float, default=0.25, help="Minimum blended score for suggestions (default: 0.25)")
    ap.add_argument("--suggest_perparent", type=int, default=2, help="Max suggestions per parent (default: 2)")
    # misc
    ap.add_argument("--out", default="results.json", help="Write results to this JSON file (default: results.json)")
    return ap.parse_args()

# ============ Normalization helpers ============
def _norm(s: str) -> str:
    return str(s).strip().lower()

def _canon_parent(p: str) -> str:
    m = {
        "outdoor": "Outdoors",
        "outdoors": "Outdoors",
        "lifestyle": "Lifestyle",
        "culinary": "Culinary",
        "wellness": "Wellness",
        "life enrichment": "Life Enrichment",
        "entertainment": "Entertainment",
        "arts & culture": "Arts & Culture",
        "arts and culture": "Arts & Culture",
    }
    return m.get(_norm(p), p)

# ============ Embedding helpers + caches ============
EMBED_CACHE_FILE = "embed_token_cache.json"
EMBED_CACHE: Dict[str, List[float]] = {}
EMBED_DIM: int | None = None

def _load_embed_cache():
    global EMBED_CACHE
    p = pathlib.Path(EMBED_CACHE_FILE)
    if p.exists():
        try:
            EMBED_CACHE = json.loads(p.read_text())
        except Exception:
            EMBED_CACHE = {}

def _save_embed_cache():
    try:
        pathlib.Path(EMBED_CACHE_FILE).write_text(json.dumps(EMBED_CACHE))
    except Exception:
        pass

def embed(text: str) -> List[float]:
    body = json.dumps({"inputText": text})
    resp = brt.invoke_model(
        modelId=EMBED_MODEL,
        body=body,
        contentType="application/json",
        accept="application/json",
    )
    payload = json.loads(resp["body"].read())
    vec = payload.get("embedding") or payload.get("vector") or payload.get("embeddingVector")
    if not vec:
        raise RuntimeError(f"No embedding returned for text: {text[:60]}...")
    return vec

def _embed_dim_probe() -> int:
    v = embed("probe-dimension")
    return len(v)

def embed_with_cache(token: str) -> List[float]:
    key = token.strip().lower()
    if key in EMBED_CACHE:
        return EMBED_CACHE[key]
    v = embed(token)
    EMBED_CACHE[key] = v
    return v

def embed_weighted_labels(label_triples: List[Tuple[str,float,float]]) -> List[float]:
    global EMBED_DIM
    if EMBED_DIM is None:
        EMBED_DIM = _embed_dim_probe()
    vec_sum = np.zeros(EMBED_DIM, dtype=np.float32)
    w_sum = 0.0
    for name, conf, w in label_triples:
        v = np.array(embed_with_cache(name), dtype=np.float32)
        if v.shape[0] != EMBED_DIM:
            # Skip vectors with unexpected dims (defensive)
            continue
        vec_sum += v * w
        w_sum += w
    if w_sum <= 1e-9:
        return [0.0] * EMBED_DIM
    return (vec_sum / w_sum).tolist()

def cosine(a: List[float], b: List[float]) -> float:
    A, B = np.array(a), np.array(b)
    denom = (np.linalg.norm(A) * np.linalg.norm(B)) or 1e-9
    return float(A.dot(B) / denom)

# ============ Catalog vector cache with dim validation ============
CACHE_FILE = "passions_vectors_cache.json"

def _cache_key() -> str:
    canon_catalog = [{"tag": i["tag"], "parent": _canon_parent(i["parent"])} for i in PASSIONS]
    payload = {"model": EMBED_MODEL, "region": AWS_REGION, "catalog": canon_catalog}
    return hashlib.sha256(json.dumps(payload, sort_keys=True).encode("utf-8")).hexdigest()

def load_or_build_catalog_vectors() -> List[Dict[str, Any]]:
    global EMBED_DIM
    if EMBED_DIM is None:
        EMBED_DIM = _embed_dim_probe()
    p = pathlib.Path(CACHE_FILE)
    if p.exists():
        cache = json.loads(p.read_text())
        if cache.get("key") == _cache_key():
            vectors = cache.get("vectors", [])
            if vectors and len(vectors[0]["vector"]) == EMBED_DIM:
                return vectors
    print("Embedding PASSIONS catalog once (caching)...")
    vectors = []
    for item in PASSIONS:
        tag = item["tag"]
        parent = _canon_parent(item["parent"])
        seed = f"{tag} — {parent} passion for travel/lifestyle."
        vectors.append({"tag": tag, "parent": parent, "vector": embed(seed)})
    pathlib.Path(CACHE_FILE).write_text(json.dumps({"key": _cache_key(), "vectors": vectors}))
    return vectors

# ============ Label weighting ============
GENERIC_LABELS = {
    "nature","outdoors","plant","vegetation","land","tree","scenery","landscape",
    "person","people","sky","cloud","daylight","floor","indoors","urban","clothing"
}

def normalize_conf(x: float | str) -> float:
    try:
        return max(0.0, min(1.0, float(x)))
    except Exception:
        return 0.0

def label_weight(name: str, conf: float | str) -> float:
    w = normalize_conf(conf)
    n = _norm(name)
    if n in GENERIC_LABELS:
        w *= 0.10  # damp generic words hard for vibe
    # modest boost for explicit cues
    if n in {
        # water/beach
        "surf","surfing","sea","wave","waves","shoreline","coast","kayak","canoe","scuba","snorkel",
        "paddleboard","paddleboarding","boat","sailing","yacht","cruise ship","reef","coral reef","aquarium","sea life",
        # mountain/snow
        "ski","snowboard","snow","powder","tent","backpack","climb","rock climbing","mountain","alpine","peak",
        # city/landmarks
        "city","landmark","monument","architecture","museum","skyline",
        # culinary
        "brunch","restaurant","coffee","wine","cocktail","bar","mixology","beer","cafeteria","meal","croissant","cup","seafood",
        # motorsport
        "formula one","f1","grand prix","auto racing","race car",
        # wellness
        "yoga","meditation","mindfulness","spa","healthy food",
        # wildlife/safari
        "wildlife","animal","safari","deer","bear","bird","cattle","cow","antelope","fish",
        # family/kids
        "child","children","boy","girl","teddy bear","toy",
        # adventure
        "ziplining","zipline","helmet","harness","adventure",
        # boating specific
        "rowboat","lifejacket","vest",
    }:
        w *= 1.25
    return w

def pick_top_labels_scored(aws_labels: List[Dict[str, Any]], max_labels: int = 16, mass: float = 0.95) -> List[Tuple[str,float,float]]:
    items = []
    for d in aws_labels:
        if "label" not in d or "score" not in d:
            continue
        name = str(d["label"])
        conf = normalize_conf(d["score"])
        w = label_weight(name, conf)
        if w > 0:
            items.append((name, conf, w))
    items.sort(key=lambda x: x[2], reverse=True)
    total = sum(w for _,_,w in items) or 1.0
    picked, acc = [], 0.0
    for tup in items:
        picked.append(tup)
        acc += tup[2]
        if len(picked) >= max_labels or (acc / total) >= mass:
            break
    return picked

# ============ Aliases & Domains ============
ALIASES = {
    "formula one": ["f1","grand prix","gp","formula-1","formula1","open-wheel","race car","auto racing","racetrack","race track","pit lane","grid"],
    "surfing": ["surf","surfer","longboard","shortboard","beach break"],
    "sea waves": ["wave","waves","swell"],
    "kayak": ["canoe","kayaking","canoeing"],
    "paddleboard": ["paddleboarding","sup","stand up paddle"],
    "scuba": ["snorkel","snorkeling","snorkeller","snorkeler","reef","coral reef","aquarium","sea life"],
    "snow": ["snowy","snowfall","powder"],
    "backpack": ["backpacking"],
    "ski": ["skiing","snowboard","snowboarding"],
    "climb": ["rock climbing","bouldering"],
    "brunch": ["breakfast","brunching"],
    "dining table": ["table setting","tablescape"],
    "restaurant": ["bistro","eatery","fine dining","cafeteria"],
    "coffee": ["cafe","espresso","latte","cappuccino","cup"],
    "cocktail": ["mixology","bar"],
    "yoga": ["vinyasa","hatha"],
    "mindfulness": ["meditation"],
    "animal": ["wildlife","fauna","safari","game drive","cattle","cow","antelope","fish"],
    "landmark": ["monument","historic site","iconic site","skyline","urban"],
}

# --- PATCH: expand family & home interior synonyms ---
ALIASES.update({
    # family terms (helps trigger family domains/rules even if Rek spits variants)
    "family": ["parents","mother","father","mom","dad","grandparent","grandma","grandpa"],

    # home interior synonyms
    "living room": ["sofa","couch","lounge","sitting room","family room"],
    "house": ["home","housing","residence"],

    # baby synonyms show up a lot
    "baby": ["newborn","infant"],
})

def expand_with_aliases(labels_raw: List[str]) -> Set[str]:
    L = {_norm(x) for x in labels_raw}
    expanded = set(L)
    for base, syns in ALIASES.items():
        b = _norm(base); S = {_norm(s) for s in syns}
        if b in L or any(s in L for s in S):
            expanded.add(b); expanded |= S
    return expanded

DOMAINS: Dict[str, Dict[str, Any]] = {
    "motorsport": {
        "keywords": {"formula one","f1","grand prix","auto racing","race car","racetrack","race track","pit lane","grid"},
        "prefer_tags": {"Formula 1","Classic Cars"},
        "prefer_parents": {"Entertainment"},
        "suppress_tags": {"Soccer","Football","Basketball","Baseball","Golf","Running","Trail Running","Tennis"},
        "suppress_parents": {"Culinary","Wellness","Life Enrichment","Arts & Culture"},
    },
    "surf_water": {
        "keywords": {"surfing","surf","sea","wave","waves","beach","swell","kayak","canoe","scuba","snorkel",
                     "paddleboard","paddleboarding","boat","sailing","yacht","cruise ship","reef","coral reef","aquarium","sea life"},
        "prefer_tags": {"Surf","Water Sports","Swimming","Scuba & Snorkel","Kayak & Canoe","Paddleboarding","Sailing & Boating","Beach Retreat","Waterfront"},
        "prefer_parents": {"Outdoors","Lifestyle"},
        "suppress_tags": {"Ski & Snowboard","Rock Climbing","Trail Running","Mountains","Video Games","Tennis"},
        "suppress_parents": {"Arts & Culture","Life Enrichment"},
    },
    "culinary": {
        "keywords": {"brunch","dining table","restaurant","bistro","eatery","food","coffee","cafe","espresso","latte",
                     "wine","cocktail","mixology","beer","bar","cafeteria","meal","croissant","cup","seafood"},
        "prefer_tags": {"Restaurants","Fine Dining","Local Cuisine","Foodie Trends","Coffee","Wine","Beer","Spirits","Mixology","Cooking & Baking"},
        "prefer_parents": {"Culinary"},
        "suppress_tags": {"Ski & Snowboard","Surf","Rock Climbing","Formula 1"},
        "suppress_parents": {"Outdoors","Wellness"},
    },
    "mountain_snow_camp": {
        "keywords": {"mountain","tent","backpack","snow","powder","glacier","alpine","peak"},
        "prefer_tags": {"Hike & Glamp","Mountains","Rock Climbing","Trail Running","Winter Sports"},
        "prefer_parents": {"Outdoors","Lifestyle","Entertainment"},
        "suppress_tags": {"Scuba & Snorkel","Kayak & Canoe","Sailing & Boating","Beach Retreat","Waterfront"},
        "suppress_parents": {"Culinary"},
    },
    "wildlife_safari": {
        "keywords": {"wildlife","animal","fauna","safari","game drive","elephant","lion","zebra","giraffe","cattle","cow","antelope","sea life","fish"},
        "prefer_tags": {"Wildlife","Safari","Art & Photography","Fish"},
        "prefer_parents": {"Outdoors","Arts & Culture"},
        "suppress_tags": {"Ski & Snowboard","Surf","Video Games"},
        "suppress_parents": set(),
    },
    "city_landmarks": {
        "keywords": {"city","landmark","monument","historic site","architecture","museum","skyline","urban"},
        "prefer_tags": {"City Exploration","Landmarks","Museums","Architecture","Art & Photography","Restaurants","Coffee","Shopping","Local Culture"},
        "prefer_parents": {"Lifestyle","Arts & Culture","Culinary"},
        "suppress_tags": {"Ski & Snowboard","Surf"},
        "suppress_parents": set(),
    },
    "wellness": {
        "keywords": {"yoga","meditation","mindfulness","spa","sleep","longevity","self-care","pamper","healthy food","fitness","cardio","strength training"},
        "prefer_tags": {"Yoga","Mindfulness","Self-care & Pamper","Healthy Food","Sleep","Longevity","Cardio","Strength Training"},
        "prefer_parents": {"Wellness"},
        "suppress_tags": {"Formula 1","Gambling"},
        "suppress_parents": {"Entertainment"},
    },
    "arts_culture": {
        "keywords": {"museum","theater","art","photography","painting","sculpting","crafts","literature","fashion","beauty","shopping","historic","politics"},
        "prefer_tags": {"Museums","Theater","Art & Photography","Painting & Drawing","Sculpting","Crafts","Literature","Fashion","Beauty","Shopping","Historical Events","Politics & Current Events","Local Culture","Architecture"},
        "prefer_parents": {"Arts & Culture"},
        "suppress_tags": {"Ski & Snowboard","Surf"},
        "suppress_parents": {"Outdoors"},
    },
    "family_kids": {
        "keywords": {"child","children","girl","boy","kid","kids","teddy bear","toy"},
        "prefer_tags": {"Kids & Grandkids","Museums","Theme Parks","Friends"},
        "prefer_parents": {"Life Enrichment","Arts & Culture","Entertainment"},
        "suppress_tags": {"Spirits","Gambling"},
        "suppress_parents": set(),
    },
    "adventure": {
        "keywords": {"ziplining","zipline","helmet","harness","adventure"},
        "prefer_tags": {"Adventure Sports","Rock Climbing","Trail Running","Kayak & Canoe","Paddleboarding"},
        "prefer_parents": {"Lifestyle","Outdoors"},
        "suppress_tags": {"Fine Dining","Museums"},
        "suppress_parents": {"Culinary","Arts & Culture"},
    },
    "sports_general": {
        "keywords": {"soccer","football","basketball","baseball","tennis","golf","running"},
        "prefer_tags": {"Soccer","Football","Basketball","Baseball","Tennis","Golf","Running","Trail Running"},
        "prefer_parents": {"Entertainment","Lifestyle","Wellness"},
        "suppress_tags": set(),
        "suppress_parents": {"Culinary","Arts & Culture"},
    },
    
    # ==== NEW CONTEXTUAL DOMAINS START Addtional FROM AI====
    "marine_life": {
        "keywords": {"reef", "coral", "fish", "turtle", "scuba", "snorkel", "aquarium", "sea life"},
        "prefer_tags": {"Scuba & Snorkel", "Kayak & Canoe", "Surf", "Water Sports", "Paddleboarding"},
        "prefer_parents": {"Outdoors", "Lifestyle"},
        "suppress_tags": {"Ski & Snowboard", "Rock Climbing", "Trail Running"},
        "suppress_parents": {"Culinary"},
    },
    "culinary_foodie": {
        "keywords": {"restaurant", "coffee", "plate", "meal", "bakery", "wine", "food", "croissant"},
        "prefer_tags": {"Restaurants", "Coffee", "Fine Dining", "Foodie Trends", "Cooking & Baking"},
        "prefer_parents": {"Culinary"},
        "suppress_tags": {"Running", "Trail Running", "Golf"},
        "suppress_parents": {"Outdoors"},
    },
    "festival_concert": {
        "keywords": {"concert", "festival", "stage", "microphone", "band", "audience", "music"},
        "prefer_tags": {"Festivals & Concerts", "Music", "Artist Fandom", "Theater"},
        "prefer_parents": {"Entertainment", "Arts & Culture"},
        "suppress_tags": {"Yoga", "Meditation", "Healthy Food"},
        "suppress_parents": {"Wellness"},
    },
    "architecture_city": {
        "keywords": {"city", "building", "architecture", "bridge", "museum", "urban", "skyline"},
        "prefer_tags": {"Architecture", "City Exploration", "Museums", "Landmarks"},
        "prefer_parents": {"Arts & Culture", "Lifestyle"},
        "suppress_tags": {"Wildlife", "Safari", "Kayak & Canoe"},
        "suppress_parents": {"Outdoors"},
    },
    "desert_travel": {
        "keywords": {"sand", "dune", "camel", "desert", "cactus"},
        "prefer_tags": {"Adventure Sports", "Safari", "Wildlife"},
        "prefer_parents": {"Outdoors", "Lifestyle"},
        "suppress_tags": {"Surf", "Water Sports"},
        "suppress_parents": {"Culinary"},
    },
    "forest_camp": {
        "keywords": {"tree", "tent", "backpack", "campfire", "forest", "trail", "woods"},
        "prefer_tags": {"Hike & Glamp", "Hike", "Trail Running", "Mountains"},
        "prefer_parents": {"Outdoors"},
        "suppress_tags": {"Scuba & Snorkel", "Surf"},
        "suppress_parents": {"Culinary"},
    },

    "art_creativity": {
        "keywords": {"art", "painting", "sculpture", "drawing", "canvas", "theater"},
        "prefer_tags": {"Art & Photography", "Painting & Drawing", "Sculpting", "Theater", "Crafts"},
        "prefer_parents": {"Arts & Culture"},
        "suppress_tags": {"Ski & Snowboard", "Football"},
        "suppress_parents": {"Outdoors"},
    },
    "wildlife_park": {
        "keywords": {"animal", "deer", "elephant", "lion", "safari", "bird", "wildlife"},
        "prefer_tags": {"Wildlife", "Safari", "Fish"},
        "prefer_parents": {"Outdoors", "Arts & Culture"},
        "suppress_tags": {"Ski & Snowboard", "Surf"},
        "suppress_parents": {"Culinary"},
    },
    "luxury_lifestyle": {
        "keywords": {"resort", "spa", "villa", "pool", "champagne", "sunset"},
        "prefer_tags": {"Beach Retreat", "Waterfront", "Sailing & Boating", "Wine"},
        "prefer_parents": {"Lifestyle", "Culinary"},
        "suppress_tags": {"Hike", "Running"},
        "suppress_parents": {"Outdoors"},
    },
    "family_fun": {
        "keywords": {"child", "toy", "family", "kids", "playground", "amusement park"},
        "prefer_tags": {"Kids & Grandkids", "Friends", "Museums", "Theme Parks"},
        "prefer_parents": {"Life Enrichment", "Entertainment"},
        "suppress_tags": {"Spirits", "Gambling"},
        "suppress_parents": {"Culinary"},
    },
    "adventure_extreme": {
        "keywords": {"zipline", "climbing", "skiing", "surfing", "bungee"},
        "prefer_tags": {"Adventure Sports", "Rock Climbing", "Surf", "Kayak & Canoe"},
        "prefer_parents": {"Lifestyle", "Outdoors"},
        "suppress_tags": {"Fine Dining", "Museums"},
        "suppress_parents": {"Culinary"},
    },
    # ==== ADDITIONAL DOMAIN: CULTURAL TRAVEL ====
    "cultural_travel": {
        "keywords": {
            "monument", "temple", "church", "cathedral", "palace", "ruins",
            "heritage", "historic site", "architecture", "museum", "local culture"
        },
        "prefer_tags": {
            "Landmarks", "Museums", "Historical Events", "Local Culture",
            "Art & Photography", "Architecture", "Politics & Current Events"
        },
        "prefer_parents": {"Arts & Culture", "Lifestyle"},
        "suppress_tags": {"Surf", "Kayak & Canoe", "Ski & Snowboard"},
        "suppress_parents": {"Culinary", "Outdoors"},
    },
    "home_family": {
    # Family indoors at home
    "keywords": {
        "family","baby","newborn","infant","toddler","child","children","kid","kids","boy","girl",
        "living room","room","indoors","house","home","sofa","couch","portrait","photography"
    },
    "prefer_tags": {"Kids & Grandkids","Friends"},
    "prefer_parents": {"Life Enrichment"},
    # steer away from out-of-home or background-y picks in a home portrait
    "suppress_tags": {"Architecture","City Exploration","Museums","Golf","Restaurants","Fine Dining"},
    "suppress_parents": {"Outdoors","Culinary"},
    },
    # ==== END CULTURAL TRAVEL DOMAIN ====
    }

def detect_domains_scored(label_triples: List[Tuple[str,float,float]]) -> Dict[str, float]:
    weight_by_token: Dict[str, float] = {}
    for name, conf, w in label_triples:
        k = _norm(name)
        weight_by_token[k] = weight_by_token.get(k, 0.0) + w
    hits: Dict[str, float] = {}
    for dname, cfg in DOMAINS.items():
        s = sum(weight_by_token.get(_norm(kw), 0.0) for kw in cfg["keywords"])
        if s > 0:
            hits[dname] = s
    return hits

# ============ Rules / Blending ============
COSINE_WEIGHT = 0.55   # vibe-first
RULES_WEIGHT  = 0.45
HARD_SUPPRESS_SCORE = -0.45

def rules_score(tag: str, parent: str, labels_set: Set[str], domain_hits: Dict[str, float]) -> float:
    t = _norm(tag); p = _canon_parent(parent)
    score = 0.0

    def seen(need: Set[str]) -> bool:
        return any(_norm(x) in labels_set for x in need)

    # Guardrails (tiny nudges; avoid over-inference)
    if t == "ski & snowboard" and not seen({"ski","skiing","snowboard","snowboarding"}):
        score += HARD_SUPPRESS_SCORE
    if t == "water sports" and not seen({"surf","surfboard","kayak","canoe","paddleboard","paddleboarding"}):
        score -= 0.30
    if t == "art & photography" and seen({"camera","tripod","photography","portrait"}):
        score += 0.20
    if t == "wildlife" and not seen({"deer","bird","bear","animal","wildlife","safari","cattle","cow","antelope","fish"}):
        score -= 0.35
    if t == "hike & glamp" and ("tent" in labels_set) and (("mountain" in labels_set) or ("backpack" in labels_set)):
        score += 0.25
    # New Rules     
    if t == "yoga" and not seen({"mat", "pose", "stretch"}):
        score -= 0.15
    if t == "surf" and seen({"sea", "wave", "board"}):
        score += 0.25
    if t == "coffee" and seen({"cup", "cafe", "espresso"}):
        score += 0.15
    if t == "kids & grandkids" and seen({"child", "toy", "family"}):
        score += 0.3    
    
    # Soft beach vibe nudge
    if any(k in labels_set for k in {"sea","shoreline","coast","beach","water"}):
        if t in {"beach retreat","waterfront","swimming","paddleboarding"}:
            score += 0.15
    # --- PATCH: helpers for family/home/<USER>
    has_family_signal = any(x in labels_set for x in {
        "family","baby","newborn","infant","toddler","child","children","kid","kids","boy","girl"
    })
    has_portrait = any(x in labels_set for x in {"portrait","photography"})
    has_home = any(x in labels_set for x in {
        "indoors","living room","room","house","home","sofa","couch"
    })

    # Baby / child portrait: push Kids & Grandkids above background/food/drink noise
    if has_family_signal and has_portrait:
        if t == "kids & grandkids":
            score += 0.55
        if t == "friends":
            score += 0.10
        if t in {"architecture","city exploration","museums","wine","spirits","fine dining"}:
            score -= 0.35
        if t == "art & photography" and not seen({"camera","tripod","dslr","lens"}):
            score -= 0.10

    # Family at home (even without “portrait”), prioritize family vibes, suppress travel-y picks
    if has_family_signal and has_home:
        if t == "kids & grandkids":
            score += 0.65
        if t == "friends":
            score += 0.20
        if t in {"architecture","city exploration","museums","golf","restaurants","fine dining"}:
            score -= 0.40
        if t == "art & photography" and not seen({"camera","tripod","dslr","lens"}):
            score -= 0.10
            
            
            
    # Domain routing
    for dname, strength in domain_hits.items():
        cfg = DOMAINS[dname]
        scale = max(0.0, min(1.0, strength / 2.0))  # normalize domain strength
        if t in {x.lower() for x in cfg["prefer_tags"]}:
            score += 0.50 * scale
        if _canon_parent(p) in cfg["prefer_parents"]:
            score += 0.15 * scale
        if t in {x.lower() for x in cfg["suppress_tags"]}:
            score -= 0.35 * scale
        if _canon_parent(p) in cfg["suppress_parents"]:
            score -= 0.25 * scale

    # If the ONLY domain detected is Culinary, avoid floating Architecture
    if ("culinary" in domain_hits) and (len(domain_hits) == 1):
        if _canon_parent(p) == "Arts & Culture":
            score -= 0.15

    # Generic balancing — avoid overconfidence -----
    score = max(-1.0, min(1.0, score))
    
    return max(-1.0, min(1.0, score))

def blend_scores(cos: float, rule: float) -> float:
    rule01 = (rule + 1.0) / 2.0     # map [-1,1] -> [0,1]
    blended = (COSINE_WEIGHT * cos) + (RULES_WEIGHT * rule01)
    return max(0.0, min(1.0, blended))

# ============ Core ============
def score_all_passions(
    aws_labels_scored: List[Dict[str, Any]],
    catalog_vecs: List[Dict[str, Any]]
) -> Tuple[List[Dict[str, Any]], Dict[str, float], Set[str]]:
    picked = pick_top_labels_scored(aws_labels_scored, max_labels=16, mass=0.95)
    if not picked:
        return [], {}, set()
    labels_raw = [name for (name, _, _) in picked]
    label_set = expand_with_aliases(labels_raw)
    domain_hits = detect_domains_scored(picked)
    qvec = embed_weighted_labels(picked)

    rows = []
    for p in catalog_vecs:
        cos_score = cosine(qvec, p["vector"])
        rule_score = rules_score(p["tag"], p["parent"], label_set, domain_hits)
        final = blend_scores(cos_score, rule_score)
        rows.append({"tag": p["tag"], "parent": p["parent"], "score": final})
    rows.sort(key=lambda x: x["score"], reverse=True)
    return rows, domain_hits, label_set

def select_top_diverse(
    rows: List[Dict[str, Any]],
    k: int,
    min_score: float,
    per_parent_cap: int,
    exclude_tags: Set[str] | None = None
) -> List[Dict[str, Any]]:
    exclude_tags = exclude_tags or set()
    parent_counts: Dict[str, int] = {}
    out: List[Dict[str, Any]] = []
    for r in rows:
        if r["score"] < min_score:
            continue
        if r["tag"] in exclude_tags:
            continue
        c = parent_counts.get(r["parent"], 0)
        if c >= per_parent_cap:
            continue
        parent_counts[r["parent"]] = c + 1
        out.append({"tag": r["tag"], "parent": r["parent"], "score": round(r["score"], 3)})
        if len(out) >= k:
            break
    return out

def match_and_suggest_for_image(
    aws_labels_scored: List[Dict[str, Any]],
    catalog_vecs: List[Dict[str, Any]],
    top_k: int,
    min_report_score: float,
    per_parent_cap: int,
    suggest_k: int,
    suggest_min_score: float,
    suggest_per_parent_cap: int
) -> Dict[str, Any]:
    t0 = time.time()
    rows, domain_hits, kept_labels_set = score_all_passions(aws_labels_scored, catalog_vecs)

    if not rows:
        return {
            "matched": [],
            "suggested": [],
            "_diagnostics": {
                "latency_ms": 0.0,
                "kept_labels": [],
                "domains": [],
                "catalog_len": len(PASSIONS),
                "note": "No informative labels after filtering"
            }
        }

    # primary matches
    matched = select_top_diverse(
        rows, k=top_k, min_score=min_report_score, per_parent_cap=per_parent_cap
    )
    matched_tags = {m["tag"] for m in matched}

    # suggestions: next best rows (excluding already matched)
    suggestions = select_top_diverse(
        rows, k=suggest_k, min_score=suggest_min_score,
        per_parent_cap=suggest_per_parent_cap, exclude_tags=matched_tags
    )

    return {
        "matched": matched,
        "suggested": suggestions,
        "_diagnostics": {
            "latency_ms": round((time.time()-t0)*1000, 1),
            "kept_labels": sorted(list(kept_labels_set))[:24],  # show sample
            "domains": sorted(domain_hits.items()),
            "catalog_len": len(PASSIONS)
        }
    }

# ============ IO helpers ============
def load_images_from_json(path: str) -> Dict[str, List[Dict[str, Any]]]:
    p = pathlib.Path(path)
    if not p.exists():
        print(f"ERROR: labels file not found: {path}")
        sys.exit(1)
    try:
        data = json.loads(p.read_text())
        if not isinstance(data, dict):
            raise ValueError("Top-level JSON must be an object mapping image_id -> list of labels.")
        for k, v in data.items():
            if not isinstance(v, list):
                raise ValueError(f"Value for key '{k}' must be a list of label dicts.")
        return data
    except Exception as e:
        print(f"ERROR reading {path}: {e}")
        sys.exit(1)
        
        
# ==== Add this to match_passion_aws_v5.py (bottom of file, above __main__) ====
# Global catalog cache so we don’t rebuild it every request
_CATALOG_VECS = None
_MATCHER_WARMED = False

def init_matcher() -> None:
    """
    Warm the matcher once:
      - load token cache
      - build/reuse PASSIONS vectors (Bedrock Titan v2)
    Safe to call multiple times.
    """
    global _CATALOG_VECS, _MATCHER_WARMED
    if _MATCHER_WARMED and _CATALOG_VECS:
        return
    _load_embed_cache()
    _CATALOG_VECS = load_or_build_catalog_vectors()
    _MATCHER_WARMED = True

def match_labels_simple(
    aws_labels_scored, 
    top_k: int = 3,
    min_report_score: float = 0.40,
    per_parent_cap: int = 2,
    suggest_k: int = 2,
    suggest_min_score: float = 0.30,
    suggest_per_parent_cap: int = 1
):
    """
    Minimal entry point for FastAPI:
      aws_labels_scored: list like [{"label":"Sea","score":0.999}, ...]
    Returns: {"matched":[...], "suggested":[...], "_diagnostics":{...}}
    """
    global _CATALOG_VECS
    if _CATALOG_VECS is None:
        init_matcher()
    return match_and_suggest_for_image(
        aws_labels_scored,
        _CATALOG_VECS,
        top_k,
        min_report_score,
        per_parent_cap,
        suggest_k,
        suggest_min_score,
        suggest_per_parent_cap,
    )
    
    
# ============ Main ============
if __name__ == "__main__":
    args = parse_args()
    _load_embed_cache()
    try:
        catalog_vecs = load_or_build_catalog_vectors()
    except Exception as e:
        print("ERROR embedding PASSIONS catalog:", e)
        sys.exit(1)

    images = load_images_from_json(args.labels)

    results = {}
    print("\n===== Passion Matcher (batch, matches + suggestions) =====\n")
    for name, labels in images.items():
        try:
            print(f"▶ {name}")
            res = match_and_suggest_for_image(
                labels, catalog_vecs,
                top_k=args.topk,
                min_report_score=args.minscore,
                per_parent_cap=args.perparent,
                suggest_k=args.suggestk,
                suggest_min_score=args.suggestminscore,
                suggest_per_parent_cap=args.suggest_perparent
            )
            results[name] = res
            print(json.dumps(res, indent=2))
            print("-" * 60)
        except Exception as e:
            print(f"[ERROR] {name}: {e}")
            print("-" * 60)

    _save_embed_cache()
    try:
        pathlib.Path(args.out).write_text(json.dumps(results, indent=2))
        print(f"\nSaved results -> {args.out}")
    except Exception as e:
        print(f"\n[WARN] Could not write results to {args.out}: {e}")
