"""Tags Service - Fora-Marriott Pseudo Microservice."""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core import get_settings, init_sentry, setup_logging
from app.tags import router as tags_router

# Get settings
settings = get_settings()

# Setup logging and monitoring
setup_logging("tags")
init_sentry("tags")

# Create FastAPI application
app = FastAPI(
    title=f"{settings.app_name} - Tags Service",
    description="Image tagging and analysis microservice",
    version=settings.version,
    debug=settings.debug,
    openapi_prefix="/tags",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Include tags routes
app.include_router(tags_router)
