# =========================
# Stage 1: Builder
# =========================
FROM python:3.11-slim AS builder

# Environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

WORKDIR /app

# Install build dependencies (needed for compiling packages)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN pip install --no-cache-dir uv

# Copy only dependency files (for better caching)
COPY pyproject.toml poetry.lock* ./

# Install dependencies into a temporary directory
RUN uv pip install --no-cache-dir --target=/app/deps .

# =========================
# Stage 2: Runtime
# =========================
FROM python:3.11-slim

# Environment configuration
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PORT=8002

WORKDIR /app

# Install only runtime dependencies (minimal footprint)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy Python dependencies from builder stage
COPY --from=builder /app/deps /usr/local/lib/python3.11/site-packages

# Copy project source
COPY . .

# Create and switch to non-root user
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# Health check for container readiness
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose app port
EXPOSE ${PORT}

# Default command (FastAPI via Uvicorn)
CMD ["python", "-m", "uvicorn", "app.main_images:app", "--host", "0.0.0.0", "--port", "8002"]
