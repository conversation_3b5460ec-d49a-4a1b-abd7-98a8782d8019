"""Enums for the application."""

from enum import Enum


class ProcessingState(str, Enum):
    """Image processing states."""
    
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    DONE = "DONE"
    FAILED = "FAILED"
    LABELS_SAVED = "LABELS_SAVED"  # Used in services/images.py


class BatchStatus(str, Enum):
    """Batch processing statuses."""
    
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

