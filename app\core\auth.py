"""JWT Authentication utilities."""

import uuid
from datetime import UTC, datetime, timedelta
from typing import Any

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from pydantic import BaseModel

from .config import get_settings


class TokenData(BaseModel):
    """Token data model."""

    user_id: str | None = None
    cust_id: str | None = None


class Token(BaseModel):
    """Token response model."""

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    is_new_user: bool = False


class TokenRequest(BaseModel):
    """Token request model."""

    cust_id: str | None = None

    def get_unique_identifier(self) -> str | None:
        """Get the unique identifier from any available field."""
        return self.cust_id or self.device_id


# Security scheme
security = HTTPBearer()


def create_access_token(
    data: dict[str, Any], expires_delta: timedelta | None = None
) -> str:
    """Create JWT access token."""
    settings = get_settings()

    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(minutes=settings.jwt_expire_minutes)

    to_encode.update(
        {
            "exp": expire,
            "iat": datetime.now(UTC),
            "jti": str(uuid.uuid4()),  # JWT ID for token tracking
        }
    )

    encoded_jwt = jwt.encode(
        to_encode, settings.secret_key, algorithm=settings.jwt_algorithm
    )

    return encoded_jwt


def verify_token(token: str) -> TokenData:
    """Verify and decode JWT token ."""
    settings = get_settings()

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, settings.secret_key, algorithms=[settings.jwt_algorithm]
        )

        user_id: str = payload.get("sub")
        cust_id: str = payload.get("cust_id")

        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token payload missing 'sub' claim",
                headers={"WWW-Authenticate": "Bearer"},
            )


        token_data = TokenData(user_id=user_id, cust_id=cust_id)
        return token_data
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"JWT decode error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

    


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> TokenData:
    """Get current user from JWT token."""
    return verify_token(credentials.credentials)


def create_token_response(user_id: str, unique_id: str | None = None, is_new_user: bool = False) -> Token:
    """Create token response with proper expiration."""
    settings = get_settings()

    # Create token data
    token_data = {
        "sub": user_id,
        "cust_id": unique_id,
    }

    # Create access token
    access_token = create_access_token(data=token_data)

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.jwt_expire_minutes * 60,  # Convert to seconds
        is_new_user=is_new_user,
    )
