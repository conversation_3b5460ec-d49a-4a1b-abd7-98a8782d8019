name: Deploy Microservice To AWS ECS Cluster

on:
  push:
    branches:
      - dev
      - main
      - prod

jobs:
  detect-service:
    runs-on: ubuntu-latest
    outputs:
      deploy-auth: ${{ steps.set.outputs.deploy-auth }}
      deploy-admin: ${{ steps.set.outputs.deploy-admin }}
      deploy-tag: ${{ steps.set.outputs.deploy-tag }}
      deploy-image: ${{ steps.set.outputs.deploy-image }}
      deploy-worker: ${{ steps.set.outputs.deploy-worker }}
    steps:
      - id: set
        run: |
          MSG="${{ github.event.head_commit.message }}"
          MSG_LOWER=$(echo "$MSG" | tr '[:upper:]' '[:lower:]')
          echo "Commit message: $MSG"

          # Default false
          echo "deploy-auth=false" >> $GITHUB_OUTPUT
          echo "deploy-admin=false" >> $GITHUB_OUTPUT
          echo "deploy-tag=false" >> $GITHUB_OUTPUT
          echo "deploy-image=false" >> $GITHUB_OUTPUT
          echo "deploy-worker=false" >> $GITHUB_OUTPUT

          # If commit message contains service:all, enable all deployments
          if [[ "$MSG_LOWER" == *"service:all"* ]]; then
            echo "🔐 Commit message contains 'service:all'. Deploying ALL services!" 
            echo "deploy-auth=true" >> $GITHUB_OUTPUT
            echo "deploy-admin=true" >> $GITHUB_OUTPUT
            echo "deploy-tag=true" >> $GITHUB_OUTPUT
            echo "deploy-image=true" >> $GITHUB_OUTPUT
            echo "deploy-worker=true" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Otherwise check individually
          for svc in auth admin tag image worker; do
            if echo "$MSG_LOWER" | grep -q "service:$svc"; then
              echo "deploy-$svc=true" >> $GITHUB_OUTPUT
            fi
          done

  deploy-auth:
    needs: detect-service
    if: needs.detect-service.outputs.deploy-auth == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4   
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build & Push auth image
        id: build-auth
        run: |
          BRANCH=${GITHUB_REF_NAME}
          IMAGE=${{ steps.login-ecr.outputs.registry }}/fora-marriott-${BRANCH}:auth-api-${BRANCH}-latest
          docker build -f Dockerfile.auth -t $IMAGE .
          docker push $IMAGE
          echo "image=$IMAGE" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
      - name: Render ECS task definition
        id: render-auth
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs/${{ steps.build-auth.outputs.branch }}/${{ steps.build-auth.outputs.branch }}-auth-task-definition.json
          container-name: fora-marriott-${{ steps.build-auth.outputs.branch }}-auth-api
          image: ${{ steps.build-auth.outputs.image }}
      - name: Deploy auth service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.render-auth.outputs.task-definition }}
          service: fora-marriott-${{ steps.build-auth.outputs.branch }}-auth-service
          cluster: fora-marriott-cluster-${{ steps.build-auth.outputs.branch }}
          wait-for-service-stability: true

  deploy-admin:
    needs: detect-service
    if: needs.detect-service.outputs.deploy-admin == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build & Push admin image
        id: build-admin
        run: |
          BRANCH=${GITHUB_REF_NAME}
          IMAGE=${{ steps.login-ecr.outputs.registry }}/fora-marriott-${BRANCH}:admin-api-${BRANCH}-latest
          docker build -f Dockerfile.admin -t $IMAGE .
          docker push $IMAGE
          echo "image=$IMAGE" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
      - name: Render ECS task definition
        id: render-admin
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs/${{ steps.build-admin.outputs.branch }}/${{ steps.build-admin.outputs.branch }}-admin-task-definition.json
          container-name: fora-marriott-${{ steps.build-admin.outputs.branch }}-admin-api
          image: ${{ steps.build-admin.outputs.image }}
      - name: Deploy admin service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.render-admin.outputs.task-definition }}
          service: fora-marriott-${{ steps.build-admin.outputs.branch }}-admin-service
          cluster: fora-marriott-cluster-${{ steps.build-admin.outputs.branch }}
          wait-for-service-stability: true

  deploy-tag:
    needs: detect-service
    if: needs.detect-service.outputs.deploy-tag == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build & Push tag image
        id: build-tag
        run: |
          BRANCH=${GITHUB_REF_NAME}
          IMAGE=${{ steps.login-ecr.outputs.registry }}/fora-marriott-${BRANCH}:tag-api-${BRANCH}-latest
          docker build -f Dockerfile.tags -t $IMAGE .
          docker push $IMAGE
          echo "image=$IMAGE" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
      - name: Render ECS task definition
        id: render-tag
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs/${{ steps.build-tag.outputs.branch }}/${{ steps.build-tag.outputs.branch }}-tag-task-definition.json
          container-name: fora-marriott-${{ steps.build-tag.outputs.branch }}-tag-api
          image: ${{ steps.build-tag.outputs.image }}
      - name: Deploy tag service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.render-tag.outputs.task-definition }}
          service: fora-marriott-${{ steps.build-tag.outputs.branch }}-tag-service
          cluster: fora-marriott-cluster-${{ steps.build-tag.outputs.branch }}
          wait-for-service-stability: true

  deploy-image:
    needs: detect-service
    if: needs.detect-service.outputs.deploy-image == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build & Push image microservice
        id: build-image
        run: |
          BRANCH=${GITHUB_REF_NAME}
          IMAGE=${{ steps.login-ecr.outputs.registry }}/fora-marriott-${BRANCH}:image-api-${BRANCH}-latest
          docker build -f Dockerfile.images -t $IMAGE .
          docker push $IMAGE
          echo "image=$IMAGE" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
      - name: Render ECS task definition
        id: render-image
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs/${{ steps.build-image.outputs.branch }}/${{ steps.build-image.outputs.branch }}-image-task-definition.json
          container-name: fora-marriott-${{ steps.build-image.outputs.branch }}-image-api
          image: ${{ steps.build-image.outputs.image }}
      - name: Deploy image service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.render-image.outputs.task-definition }}
          service: fora-marriott-${{ steps.build-image.outputs.branch }}-image-service
          cluster: fora-marriott-cluster-${{ steps.build-image.outputs.branch }}
          wait-for-service-stability: true

  deploy-worker:
    needs: detect-service
    if: needs.detect-service.outputs.deploy-worker == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build & Push worker image
        id: build-worker
        run: |
          BRANCH=${GITHUB_REF_NAME}
          IMAGE=${{ steps.login-ecr.outputs.registry }}/fora-marriott-${BRANCH}:worker-api-${BRANCH}-latest
          docker build -f Dockerfile.worker -t $IMAGE .
          docker push $IMAGE
          echo "image=$IMAGE" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
      - name: Render ECS task definition
        id: render-worker
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs/${{ steps.build-worker.outputs.branch }}/${{ steps.build-worker.outputs.branch }}-worker-task-definition.json
          container-name: fora-marriott-${{ steps.build-worker.outputs.branch }}-worker
          image: ${{ steps.build-worker.outputs.image }}
      - name: Deploy worker service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.render-worker.outputs.task-definition }}
          service: fora-marriott-${{ steps.build-worker.outputs.branch }}-worker-service
          cluster: fora-marriott-cluster-${{ steps.build-worker.outputs.branch }}
          wait-for-service-stability: true
