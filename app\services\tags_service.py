"""Tags service for business logic."""

import json
from typing import List

from fastapi import Depends

from app.core.config import get_settings
from app.core.redis_service import get_redis_service
from app.services.models.tags import (AllMasterTagsResponse, ImageTags, ImageTagUpdate,
                                      MasterTag, MasterTagCategory, TagCategory,
                                      UserTagsResponse)
from app.services.repository.tags_repo import TagsRepository, get_tags_repository


class TagsService:
    """Service for tags-related business logic."""

    def __init__(self, tags_repo: TagsRepository = Depends(get_tags_repository)) -> None:
        """Initialize tags service."""
        self.tags_repo = tags_repo
        self.settings = get_settings()

    async def get_user_tags(self, user_id: str) -> UserTagsResponse:
        """Get all tags for a user's images."""
        # Get raw data from repository
        raw_data = await self.tags_repo.get_user_image_tags(user_id)

        # Transform to response format
        labels = []
        for item in raw_data:
            # Return just image_id without URL prefix
            image_tags = ImageTags(
                image_id=item['image_id'],
                tags=[
                    TagCategory(
                        parent_cat=tag["parent_cat"],
                        tags=tag["tags"]
                    )
                    for tag in item["tags"]
                ],
                suggested_tags=item["suggested_tags"]
            )
            # image_tags["aws_labels"] = item['aws_labels']
            labels.append({ **image_tags.model_dump(), "aws_labels": item["aws_labels"], "matched_tags_raw": item["matched_tags_raw"], "suggested_tags_raw": item["suggested_tags_raw"] })

        return {
            "user_id": user_id,
            "labels": labels  # labels is already a list of dicts with image_id and tags
        }


    async def update_image_tags(self, user_id: str, updates: List[ImageTagUpdate]) -> dict:
        """Update tags for multiple images."""
        # Convert request to format expected by repository
        update_data = [
            {
                "image_id": update.image_id,
                "tags": update.tags
            }
            for update in updates
        ]

        result = await self.tags_repo.update_image_tags(user_id, update_data)

        success = result["updated_count"] > 0

        response = {
            "success": success,
            "message": f"Updated tags for {result['updated_count']} out of {result['total_requested']} images",
            "updated_images": result["updated_count"],
            "total_requested": result["total_requested"]
        }

        # Include failed updates if any
        if result["failed_updates"]:
            response["failed_updates"] = result["failed_updates"]

        return response

    async def get_all_master_tags(self) -> AllMasterTagsResponse:
        """
        Get all master tags grouped by parent category.

        Uses Redis caching to reduce DynamoDB calls:
        - Cache key: "master_tags:all"
        - TTL: 1 hour (3600 seconds)
        """
        CACHE_KEY = "master_tags:all"
        CACHE_TTL = 3600  # 1 hour

        try:
            # Try to get from Redis cache
            redis_client = get_redis_service().get_client()
            cached_data = redis_client.get(CACHE_KEY)

            if cached_data:
                # Cache hit - deserialize and return
                categories_data = json.loads(cached_data)
                categories = [
                    MasterTagCategory(**cat_data) for cat_data in categories_data
                ]
                return AllMasterTagsResponse(categories=categories)

        except Exception as e:
            # Redis error - log but continue to fetch from DB
            print(f"Redis cache error (get_all_master_tags): {e}")

        # Cache miss or Redis error - fetch from database
        raw_data = await self.tags_repo.get_all_master_tags()

        # Transform to response format
        categories = []
        for item in raw_data:
            # Add URL prefix to parent_image using IMAGE_BASE_URL
            parent_image_url = None
            if item.get("parent_image"):
                parent_image_url = f"{self.settings.image_base_url}/{item['parent_image']}"

            category = MasterTagCategory(
                parent_cat=item["parent_cat"],
                parent_image=parent_image_url,
                parent_desc=item.get("parent_desc"),
                tags=[
                    MasterTag(
                        marriott_tag=tag["marriott_tag"],
                        activity_image=f"{self.settings.image_base_url}/{tag['activity_image']}" if tag.get("activity_image") else None
                    )
                    for tag in item["tags"]
                ]
            )
            categories.append(category)

        # Cache the result in Redis
        try:
            redis_client = get_redis_service().get_client()
            # Serialize categories to JSON
            categories_data = [cat.model_dump() for cat in categories]
            redis_client.set(CACHE_KEY, json.dumps(categories_data), ex=CACHE_TTL)
        except Exception as e:
            # Redis error - log but don't fail the request
            print(f"Redis cache error (set_all_master_tags): {e}")

        return AllMasterTagsResponse(categories=categories)
    
    async def get_user_results(self, user_id: str) -> List[dict]:
        """
        Return the grouped recommendations response for a user.
        Adds IMAGE_BASE_URL prefix to parent_icon and recommendation images.
        """
        results = await self.tags_repo.get_user_recommendations(user_id)

        # Add IMAGE_BASE_URL prefix to images
        for category in results:
            # Add prefix to parent_icon
            if category.get("parent_icon"):
                category["parent_icon"] = f"{self.settings.image_base_url}/{category['parent_icon']}"

            # Add prefix to each recommendation image
            for rec in category.get("recommendations", []):
                if rec.get("image"):
                    rec["image"] = f"{self.settings.image_base_url}/{rec['image']}"
        # Sort results by parent_priority (lowest first)
        results.sort(key=lambda category: category.get("parent_priority", float('inf')))
        return results


    async def reset_user_profile(self, user_id: str) -> dict:
        """Reset user profile by deleting all images and recommendations."""
        result = await self.tags_repo.reset_user_profile(user_id)
        return result


def get_tags_service(
    tags_repo: TagsRepository = Depends(get_tags_repository)
) -> TagsService:
    """Get tags service instance."""
    return TagsService(tags_repo)
