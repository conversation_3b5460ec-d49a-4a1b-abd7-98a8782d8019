from datetime import UTC, datetime
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from app.core import get_settings
from app.core.auth import (Token, TokenData, TokenRequest, create_token_response,
                           get_current_user)
from app.services.repository.user_repo import UserService, get_user_service

router = APIRouter()
settings = get_settings()


@router.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for auth service."""
    return {
        "status": "ok",
        "timestamp": datetime.now(UTC).isoformat(),
        "service": "auth",
        "version": settings.version,
        "environment": settings.environment,
    }


@router.get("/")
async def root() -> dict[str, str]:
    """Root endpoint for auth service."""
    return {
        "message": "Fora-Marriott Auth Service",
        "version": settings.version,
        "docs": "/docs",
        "health": "/health",
    }


@router.post("/token", response_model=Token)
async def generate_token(
    request: TokenRequest,
    user_service: Annotated[UserService, Depends(get_user_service)],
) -> Token:
    """
    Generate JWT token using cust_id.

    Creates user if not exists, updates last_login if exists.
    """
    # Validate request - require at least one identifier
    unique_id = request.get_unique_identifier()

    if not unique_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of unique_id, device_id, or email is required",
        )

    # Try to find existing user
    user = await user_service.find_user_by_identifier(unique_id=unique_id)

    # If user found, update last login
    if user:
        await user_service.update_last_login(user.user_id)
        # Check if user has recommendations (is_new_user = no recommendations)
        has_recommendations = await user_service.has_recommendations(user.user_id)
        is_new_user = not has_recommendations
        return create_token_response(user.user_id, user.unique_id, is_new_user=is_new_user)

    # User not found, create new user
    try:
        new_user = await user_service.create_user(unique_id=unique_id)
        # New user always has is_new_user = True
        return create_token_response(new_user.user_id, new_user.unique_id, is_new_user=True)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}",
        )


@router.get("/me")
async def get_current_user_profile(
    current_user: Annotated[TokenData, Depends(get_current_user)],
) -> dict[str, str | None]:
    """Get current user profile from JWT token."""
    return {
        "user_id": current_user.user_id,
        "email": current_user.email,
        "message": "Token is valid",
    }


@router.post("/verify")
async def verify_token_endpoint(
    current_user: Annotated[TokenData, Depends(get_current_user)],
) -> dict[str, str | bool | None]:
    """Verify JWT token validity."""
    return {
        "valid": True,
        "user_id": current_user.user_id,
        "email": current_user.email,
    }

