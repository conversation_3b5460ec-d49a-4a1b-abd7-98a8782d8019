import redis

from app.core import get_settings


class RedisService:
    def __init__(self):
        settings = get_settings()
        self.client = redis.Redis(
            host=settings.redis_host,
            port=settings.redis_port,
            password=settings.redis_password,
            ssl=settings.redis_ssl,
            ssl_cert_reqs='none'  # Use 'required' if you have a CA cert
        )

    def get_client(self):
        return self.client

# Singleton instance
_redis_service: RedisService | None = None

def get_redis_service() -> RedisService:
    global _redis_service
    if _redis_service is None:
        _redis_service = RedisService()
    return _redis_service