# app/tags/tasks.py
import time

from app.core.celery_app import celery


@celery.task(name="tags.dummy_io")
def tags_dummy_io(n: int = 1000, sleep_ms: int = 1) -> dict:
    """
    Simulate I/O-bound small ops — sleep a little each iteration.
    Useful to exercise concurrency behavior for I/O tasks.
    """
    start = time.perf_counter()
    s = 0
    for i in range(1, n + 1):
        s += i
        time.sleep(sleep_ms / 1000.0)
    duration = time.perf_counter() - start
    return {"service": "tags", "n": n, "sum": s, "duration_seconds": duration}
