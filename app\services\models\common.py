"""Common response models."""

from typing import Generic, TypeVar

from pydantic import BaseModel

T = TypeVar("T")


class CommonResponse(BaseModel, Generic[T]):
    """Standard API response format."""

    statusCode: int
    result: T | None = None
    hasError: bool = False
    errorMsg: str = ""

    @classmethod
    def success(cls, result: T, status_code: int = 200) -> "CommonResponse[T]":
        """Create a successful response."""
        return cls(statusCode=status_code, result=result, hasError=False, errorMsg="")

    @classmethod
    def error(cls, error_msg: str, status_code: int = 500) -> "CommonResponse[None]":
        """Create an error response."""
        return cls(
            statusCode=status_code, result=None, hasError=True, errorMsg=error_msg
        )
