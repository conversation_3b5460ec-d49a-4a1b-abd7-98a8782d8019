{"family": "fora-marriott-prod-admin-api-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-prod-admin-api", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-prod:admin-api-prod-latest", "essential": true, "cpu": 512, "memory": 1024, "portMappings": [{"appProtocol": "http", "containerPort": 8004, "hostPort": 8004, "protocol": "tcp"}, {"containerPort": 50054, "hostPort": 50054, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/admin-service-fora-marriott-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "prod"}, {"name": "MAX_CONNECTIONS", "value": "50000"}]}], "volumes": []}