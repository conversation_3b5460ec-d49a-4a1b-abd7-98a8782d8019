"""Image processing service functions."""

import logging
from typing import Any

from boto3.dynamodb.conditions import Key

from app.core.database import get_db_service
from app.core.enums import ProcessingState
from app.services.utilities.common import normalize_token

logger = logging.getLogger(__name__)


async def save_aws_labels(
    user_id: str, image_id: str, aws_labels: list, rekognition_request_id: str
) -> None:
    """
    Save AWS Rekognition labels to IMAGE record.
    
    Args:
        user_id: User ID
        image_id: Image ID
        aws_labels: List of AWS labels with scores
        rekognition_request_id: AWS Rekognition request ID
    """
    try:
        db = get_db_service()
        
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="""
                SET aws_labels = :labels,
                    rekognition_request_id = :req_id,
                    processing_state = :state
            """,
            ExpressionAttributeValues={
                ":labels": aws_labels,
                ":req_id": rekognition_request_id,
                ":state": ProcessingState.LABELS_SAVED.value,
            }
        )
        
        logger.info(f"Saved AWS labels for image {image_id}: {len(aws_labels)} labels")
        
    except Exception as e:
        logger.error(f"Failed to save AWS labels for image {image_id}: {e}")
        raise


async def save_matched_tags(user_id: str, image_id: str, matched_tags: list) -> None:
    """
    Save matched Marriott tags to IMAGE record.
    
    Args:
        user_id: User ID
        image_id: Image ID
        matched_tags: List of matched Marriott tags
    """
    try:
        db = get_db_service()
        
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :tags, processing_state = :state",
            ExpressionAttributeValues={
                ":tags": matched_tags,
                ":state": ProcessingState.DONE.value,
            }
        )
        
        logger.info(f"Saved matched tags for image {image_id}: {len(matched_tags)} tags")
        
    except Exception as e:
        logger.error(f"Failed to save matched tags for image {image_id}: {e}")
        raise


async def update_batch_progress(user_id: str, batch_id: str, increment: int = 1) -> None:
    """
    Atomically increment batch progress counter.
    
    Args:
        user_id: User ID
        batch_id: Batch ID
        increment: Amount to increment (default: 1)
    """
    try:
        db = get_db_service()
        
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
            UpdateExpression="ADD progress.processed :inc",
            ExpressionAttributeValues={":inc": increment}
        )
        
        logger.debug(f"Updated batch progress for {batch_id}: +{increment}")
        
    except Exception as e:
        logger.error(f"Failed to update batch progress for {batch_id}: {e}")
        raise


async def get_aws_lookup_matches(aws_labels: list) -> list:
    """
    Query AWS_LOOKUP table for tag matches.
    
    Args:
        aws_labels: List of AWS labels to match
        
    Returns:
        List of matched tags
    """
    try:
        db = get_db_service()
        matched_tags = []
        
        for aws_label in aws_labels:
            label_name = aws_label["label"]
            label_score = aws_label["score"]
            
            # Normalize label (lowercase, remove spaces)
            normalized_label = normalize_token(label_name) # label_name.lower().replace(" ", "").replace("-", "")
            
            # Query AWS_LOOKUP table
            response = db.table.query(
                KeyConditionExpression="PK = :pk",
                ExpressionAttributeValues={
                    ":pk": f"AWS#{normalized_label}"
                }
            )
            
            # Process matches
            for item in response.get("Items", []):
                matched_tags.append({
                    "parent_cat": item["parent_cat"],
                    "marriott_tag": item["marriott_tag"],
                    "aws_label": label_name,
                    "score": label_score,
                    "tag_type": item.get("tag_type", "category1"),
                })
        
        logger.info(f"Found {len(matched_tags)} tag matches for {len(aws_labels)} AWS labels")
        return matched_tags
        
    except Exception as e:
        logger.error(f"Failed to get AWS lookup matches: {e}")
        raise


async def get_image_by_id(user_id: str, image_id: str) -> dict[str, Any] | None:
    """
    Get IMAGE record by ID.
    
    Args:
        user_id: User ID
        image_id: Image ID
        
    Returns:
        IMAGE record or None if not found
    """
    try:
        db = get_db_service()
        
        response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"}
        )
        
        return response.get("Item")
        
    except Exception as e:
        logger.error(f"Failed to get image {image_id}: {e}")
        raise


async def get_batch_images(user_id: str, batch_id: str) -> list[dict[str, Any]]:
    """
    Get all IMAGE records for a batch.
    
    Args:
        user_id: User ID
        batch_id: Batch ID
        
    Returns:
        List of IMAGE records
    """
    try:
        db = get_db_service()
        
        # Try GSI first (if exists), otherwise scan
        try:
            response = db.table.query(
                IndexName="GSI1",
                KeyConditionExpression="GSI1PK = :gsi1pk",
                ExpressionAttributeValues={
                    ":gsi1pk": f"BATCH#{batch_id}"
                }
            )
        except Exception:
            # Fall back to scan if GSI doesn't exist
            response = db.table.scan(
                FilterExpression="batch_id = :batch_id AND begins_with(SK, :sk_prefix)",
                ExpressionAttributeValues={
                    ":batch_id": batch_id,
                    ":sk_prefix": "IMAGE#"
                }
            )
        
        return response.get("Items", [])
        
    except Exception as e:
        logger.error(f"Failed to get batch images for {batch_id}: {e}")
        raise


async def check_user_image_limit(user_id: str) -> int:
    """
    Check how many images the user has uploaded.

    Args:
        user_id (str): The user's ID.

    Returns:
        int: Number of images uploaded by the user.

    Raises:
        HTTPException: If the scan operation fails.
    """
    db = get_db_service()

    try:
        response = db.table.query(
            KeyConditionExpression=Key('PK').eq(f'USER#{user_id}') & Key('SK').begins_with('IMAGE#'),
            Select='COUNT'
        )
        return response.get('Count', 0)

    except Exception as e:
        logger.error(f"Failed to scan image count for user {user_id}: {e}")
        raise
        

