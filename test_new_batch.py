#!/usr/bin/env python3
"""
Test the new batch processing.
"""

from app.core.celery_app import celery


def test_new_batch():
    """Test the new batch."""
    user_id = "bb5d29f1-cb46-411b-b233-f25cc149910f"
    batch_id = "41794fef-c1b3-455b-a27c-3105efe90cd7"
    
    print(f"Testing batch processing...")
    print(f"User ID: {user_id}")
    print(f"Batch ID: {batch_id}")
    print()
    
    try:
        # Test process_batch_task
        print("1. Starting batch processing...")
        task_result = celery.send_task(
            "images.process_batch",
            args=[user_id, batch_id],
            queue="images"
        )
        
        print(f"   Task ID: {task_result.id}")
        print("   Waiting for batch processing to complete...")
        
        # Wait for result
        result = task_result.get(timeout=300)  # 5 minutes timeout
        print(f"✓ Batch processing completed!")
        print(f"   Result: {result}")
        
        # Wait for image processing to complete
        print("   Waiting for image processing to complete...")
        import time
        time.sleep(15)
        
        # Test recommendations
        print("\n2. Generating recommendations...")
        rec_task_result = celery.send_task(
            "images.generate_user_recommendations",
            args=[user_id],
            queue="images"
        )
        
        print(f"   Task ID: {rec_task_result.id}")
        print("   Waiting for recommendations...")
        
        rec_result = rec_task_result.get(timeout=120)
        print(f"✓ Recommendations completed!")
        print(f"   Result: {rec_result}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def check_results():
    """Check the results in database."""
    from app.core.database import get_db_service
    
    user_id = "bb5d29f1-cb46-411b-b233-f25cc149910f"
    batch_id = "41794fef-c1b3-455b-a27c-3105efe90cd7"
    
    print(f"\n3. Checking results...")
    
    db = get_db_service()
    
    # Check batch
    try:
        response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"}
        )
        
        if "Item" in response:
            batch = response["Item"]
            print(f"✓ Batch status: {batch.get('status', 'UNKNOWN')}")
            print(f"  Progress: {batch.get('progress', {})}")
        else:
            print(f"✗ Batch not found")
    except Exception as e:
        print(f"✗ Error checking batch: {e}")
    
    # Check images
    try:
        response = db.table.scan(
            FilterExpression="begins_with(PK, :user_pk) AND begins_with(SK, :image_sk)",
            ExpressionAttributeValues={
                ":user_pk": f"USER#{user_id}",
                ":image_sk": "IMAGE#"
            }
        )
        
        images = response.get("Items", [])
        print(f"✓ Found {len(images)} image records")
        
        for image in images:
            image_id = image.get('image_id', 'UNKNOWN')
            state = image.get('processing_state', 'UNKNOWN')
            labels_count = len(image.get('aws_labels', []))
            tags_count = len(image.get('matched_tags', []))
            print(f"  - {image_id}: {state}, {labels_count} labels, {tags_count} tags")
            
            # Show matched tags
            matched_tags = image.get('matched_tags', [])
            for tag in matched_tags:
                parent_cat = tag.get('parent_cat', 'UNKNOWN')
                marriott_tag = tag.get('marriott_tag', 'UNKNOWN')
                score = tag.get('score', 'UNKNOWN')
                print(f"    - {parent_cat}/{marriott_tag} (score: {score})")
            
    except Exception as e:
        print(f"✗ Error checking images: {e}")
    
    # Check recommendations
    try:
        response = db.table.scan(
            FilterExpression="begins_with(PK, :user_pk) AND begins_with(SK, :rec_sk)",
            ExpressionAttributeValues={
                ":user_pk": f"USER#{user_id}",
                ":rec_sk": "RECOMMENDATION#"
            }
        )
        
        recommendations = response.get("Items", [])
        print(f"✓ Found {len(recommendations)} recommendation records")
        
        for rec in recommendations:
            categories = rec.get('categories', [])
            total_images = rec.get('total_images', 0)
            print(f"  - {len(categories)} categories, {total_images} total images")
            
            for category in categories:
                parent_cat = category.get('parent_cat', 'UNKNOWN')
                tags = category.get('tags', [])
                print(f"    - {parent_cat}: {len(tags)} tags")
                
    except Exception as e:
        print(f"✗ Error checking recommendations: {e}")


def main():
    """Main function."""
    print("🧪 New Batch Test")
    print("=" * 50)
    
    # Test the batch processing
    success = test_new_batch()
    
    # Check results
    check_results()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("❌ Test completed with errors!")


if __name__ == "__main__":
    main()
