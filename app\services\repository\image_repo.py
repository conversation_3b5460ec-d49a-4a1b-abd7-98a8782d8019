"""Upload service for database operations."""

from typing import Annotated
from botocore.exceptions import ClientError
from fastapi import Depends, HTTPException, status
from app.core.database import get_db_service
from app.services.models.image import UploadBatch
from app.services.utilities.s3_helper import S3Service, get_s3_service
from app.services.models.image import UploadInitRequest, UploadInitResponse, UploadUrl


class ImageService:
    """Service for upload-related database operations."""

    def __init__(self) -> None:
        """Initialize upload service."""
        self.db = get_db_service()

    async def create_upload_batch(
        self, user_id: str, image_names: list[str]
    ) -> UploadBatch:
        """Create a new upload batch."""
        # Create the upload batch
        batch = UploadBatch.create_new(user_id, image_names)

        try:
            # Save to DynamoDB
            self.db.table.put_item(Item=batch.to_dynamodb_item())
            return batch

        except ClientError as e:
            print(f"Error creating upload batch: {e}")
            raise Exception(f"Failed to create upload batch: {e}")

    async def get_upload_batch(self, user_id: str, batch_id: str) -> UploadBatch | None:
        """Get upload batch by user_id and batch_id."""
        try:
            response = self.db.table.get_item(
                Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"}
            )

            if "Item" in response:
                return UploadBatch.from_dynamodb_item(response["Item"])

            return None

        except ClientError as e:
            print(f"Error getting upload batch: {e}")
            return None

    async def update_batch_status(
        self, user_id: str, batch_id: str, status: str
    ) -> None:
        """Update batch status."""
        try:
            self.db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
                UpdateExpression="SET #status = :status",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={":status": status},
            )

        except ClientError as e:
            print(f"Error updating batch status: {e}")

    async def update_image_status(
        self, user_id: str, batch_id: str, image_id: str, status: str
    ) -> None:
        """Update individual image status within a batch."""
        try:
            # Get current batch
            batch = await self.get_upload_batch(user_id, batch_id)
            if not batch:
                return

            # Update the specific image status
            for image_ref in batch.image_refs:
                if image_ref.image_id == image_id:
                    image_ref.status = status
                    break

            # Update progress if image is now uploaded
            if status == "uploaded":
                uploaded_count = sum(
                    1 for ref in batch.image_refs if ref.status == "uploaded"
                )
                batch.progress.processed = uploaded_count

                # Update batch status if all images are uploaded
                if uploaded_count == batch.num_files:
                    batch.status = "ready"

            # Save updated batch
            self.db.table.put_item(Item=batch.to_dynamodb_item())

        except ClientError as e:
            print(f"Error updating image status: {e}")

    async def get_user_batches(
        self, user_id: str, limit: int = 10
    ) -> list[UploadBatch]:
        """Get user's upload batches."""
        try:
            response = self.db.table.query(
                KeyConditionExpression="PK = :pk AND begins_with(SK, :sk_prefix)",
                ExpressionAttributeValues={
                    ":pk": f"USER#{user_id}",
                    ":sk_prefix": "BATCH#",
                },
                ScanIndexForward=False,  # Most recent first
                Limit=limit,
            )

            batches = []
            for item in response.get("Items", []):
                batches.append(UploadBatch.from_dynamodb_item(item))

            return batches

        except ClientError as e:
            print(f"Error getting user batches: {e}")
            return []

    async def generate_signed_url(
        self,
        user_id: str,
        request: UploadInitRequest,
        s3_service: Annotated[S3Service, Depends(get_s3_service)],
    ) -> UploadInitResponse:
        # Validate file extensions
        invalid_files = []
        for filename in request.image_names:
            if not s3_service.validate_file_extension(filename):
                invalid_files.append(filename)

        if invalid_files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file extensions. Only jpg, jpeg, png allowed. Invalid files: {invalid_files}",
            )

        try:
            # Create upload batch in database
            batch = await self.create_upload_batch(
                user_id=user_id, image_names=request.image_names
            )

            # Generate S3 presigned URLs
            s3_keys = [ref.s3_key for ref in batch.image_refs]
            presigned_urls = s3_service.generate_multiple_presigned_urls(s3_keys)

            # Create response with upload URLs
            upload_urls = []
            for i, ref in enumerate(batch.image_refs):
                upload_urls.append(
                    UploadUrl(
                        key=ref.s3_key,
                        url=presigned_urls[i]["url"] if presigned_urls[i] else "",
                        image_id=ref.image_id,
                        original_name=ref.original_name,
                    )
                )

            return UploadInitResponse(batch_id=batch.batch_id, upload_urls=upload_urls)

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize upload: {str(e)}",
            )


# Global service instance
_upload_service: ImageService | None = None


def get_image_service() -> ImageService:
    """Get the global upload service instance."""
    global _upload_service
    if _upload_service is None:
        _upload_service = ImageService()
    return _upload_service
