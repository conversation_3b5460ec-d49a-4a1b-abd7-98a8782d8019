#!/usr/bin/env python3
"""
Simple local test for the LLM-based Passion Matcher.

Requirements:
  pip install google-generativeai
"""

import hashlib
import json
import os
import random
import threading
import time
from collections import OrderedDict
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from app.core.config import get_settings

# ==== CONFIGURATION ====
settings = get_settings()
API_KEY = settings.gemini_api_key  # <-- Put your real Gemini key here
MODEL_NAME = "models/gemini-2.0-flash" # You can change to gemini-1.5-pro or others
TOP_K = 3

# Example AWS Rekognition-style labels for a beach image
AWS_LABELS =[
                    {
                        "score": "0.9999017333984375",
                        "label": "Bag"
                    },
                    
                ]



# =======================

try:
    import google.generativeai as genai
except Exception:
    raise RuntimeError("You must install google-generativeai: pip install google-generativeai")


PASSIONS_TEXT = """
Outdoor: Wildlife, Hike & Glamp, Safari, Ski & Snowboard, Bike, Fish, Scuba & Snorkel, Kayak & Canoe, Surf, Nature, Paddleboarding
Lifestyle: Rock Climbing, Golf, Tennis, Running, Swimming, Water Sports, Beach Retreat, City Exploration, Sailing & Boating, Horse Riding, Waterfront, Mountains, Adventure Sports
Culinary: Restaurants, Foodie Trends, Fine Dining, Local Cuisine, Cooking & Baking, Wine, Beer, Spirits, Coffee, Mixology
Wellness: Cardio, Strength Training, Yoga, Medical, Mindfulness, Healthy Food, Self-care & Pamper, Home & Garden, Sleep, Longevity
Life Enrichment: Kids & Grandkids, Business & Finance, Skill Building, Pro. Development, Emerging Tech, Sustainability, Community Care, Pets, Friends
Entertainment: Football, Formula 1, Soccer, Tennis, Basketball, Baseball, Winter Sports, Golf, Music, Artist Fandom, Festivals & Concerts, Show Fandom, Movie Genres, Hollywood Culture, Actor Fandom, Animation & Fandom, Gambling, Video Games, Theme Parks
Arts & Culture: Museums, Historical Events, Politics & Current Events, Landmarks, Art & Photography, Theater, Architecture, Literature, Creation, STEM, Fashion, Beauty, Shopping, Local Culture, Painting & Drawing, Sculpting, Crafts, Classic Cars, Antiques & Vintage
"""


# ---- Simple cache ----
class LruTtlCache:
    def __init__(self, capacity=1000, ttl_seconds=3600):
        self.capacity = capacity
        self.ttl = ttl_seconds
        self.store: OrderedDict[str, Tuple[float, Any]] = OrderedDict()
        self.lock = threading.Lock()
    def get(self, key):
        with self.lock:
            item = self.store.get(key)
            if not item:
                return None
            ts, val = item
            if (time.time() - ts) > self.ttl:
                self.store.pop(key, None)
                return None
            self.store.move_to_end(key)
            return val
    def set(self, key, val):
        with self.lock:
            self.store[key] = (time.time(), val)
            self.store.move_to_end(key)
            if len(self.store) > self.capacity:
                self.store.popitem(last=False)

def stable_cache_key(labels: List[Dict[str, Any]], top_k: int) -> str:
    sorted_labels = sorted(
        [(str(l.get("label")), float(l.get("score", 0.0))) for l in labels],
        key=lambda x: (-x[1], x[0])
    )[:15]
    payload = json.dumps({"labels": sorted_labels, "k": top_k}, separators=(",", ":"), sort_keys=True)
    import hashlib
    return hashlib.md5(payload.encode("utf-8")).hexdigest()

@dataclass
class MatcherConfig:
    api_key: str
    model_name: str = MODEL_NAME
    temperature: float = 0.1
    top_p: float = 0.8
    max_output_tokens: int = 1024
    cache_capacity: int = 2000
    cache_ttl_seconds: int = 3600
    max_retries: int = 4
    timeout_seconds: int = 20

def build_prompt(aws_labels: List[Dict[str, Any]], top_k: int) -> str:
    labels_text = "\n".join([f"- {l['label']} (confidence: {float(l['score']):.2f})" for l in aws_labels])

    return f"""You are a travel passion matching expert. Given AWS Rekognition labels from a user's photo, match them to relevant travel passions.

AVAILABLE PASSIONS:
{PASSIONS_TEXT}

DETECTED LABELS FROM PHOTO:
{labels_text}

HARD CONSTRAINTS:
- Only output tags that appear verbatim as leaf items in AVAILABLE PASSIONS (e.g., "Yoga", "Tennis"). Never invent/normalize tags.
- The "tag" must be a leaf passion; the "category" must be its parent from AVAILABLE PASSIONS (never output a parent as the tag).

MATCHING RULES:
1. Prioritize specific activities over generic categories.
2. Consider context (e.g., "child + indoor + portrait" → Kids & Grandkids).
3. Beach/water signals → Beach Retreat, Surf, Water Sports (only with explicit gear/action).
4. Food/restaurant → Culinary passions.
5. Architecture/landmarks → Arts & Culture.
6. Sports equipment → specific sport passion.
7. Family/baby → Life Enrichment.

Important contextual expectations:
- Beach + Person does NOT imply Water Sports unless "Surf"/"Surfboard"/"Kayak"/"Canoe" appear.
- "Piano" without "Stage" → Music (not Theater).
- "Horse" + "Person" and no "Wildlife" → Horse Riding (if present in the catalog, else suggest only).
- "Child" + "Family" → Kids & Grandkids.
- Woods/Forest + person → Hike; with Camera/Tripod → Art & Photography.
- A generic "Person" alone does not imply fitness.
- Do NOT match Ski & Snowboard from generic Snow/Mountain alone; without gear, cap at ≤0.59 or map to Mountains/Hike & Glamp if Tent/Backpack.
- Do NOT match Wildlife unless an animal is labeled.
- Prefer Hike & Glamp over Hike when Tent + Mountain/Backpack appear.
- When several items are similar, prefer diversity across parents unless evidence strongly favors one.

# --- Sports / Water / Architecture / Context Enhancements ---
- Amusement/ride cues ("Amusement Park","Roller Coaster","Ferris wheel","Theme park") → Entertainment: Theme Parks (priority). Downrank generic Nature/Outdoors when these appear (≤0.49).
- Stadium/arena + visible field/court + crowd/bleachers → watching the sport (Entertainment), not Outdoor fitness.
- Court/ball/hoop → Entertainment: Basketball. Suppress Water Sports.
- Pitch/goals/round ball → Entertainment: Soccer; gridiron lines/helmets → Entertainment: Football.
- Tennis + crowd/stadium → Entertainment: Tennis (watching); tight player/court → Outdoor: Tennis (playing).
- Water Sports only if water + gear/action (surfboard, kayak+PADDLE, jet ski, life jacket, wave riding). Otherwise no Water Sports/Surf/Kayak & Canoe.
- Sand without sport gear → Beach Retreat.
- Pool → Wellness: Swimming (or Self-care & Pamper if spa-like). Do not map hotel pools to Waterfront.
- Architecture only when a building/structure is the primary subject (cathedral, facade, bridge, skyscraper). If game/field/court dominates → the sport, not Architecture.
- Art & Photography only with explicit art media (painting, museum, sculpture, tapestry, mosaic, canvas, gallery) OR camera intent (camera, tripod, photographer, lens, selfie). Otherwise exclude or suggest ≤0.59.
- Kayak & Canoe require boat + paddle; Surf requires board + waves.
- Waterfront requires a natural water body; pools/hotel decks ≠ Waterfront.
- Billboards/Neon/Signage with streetscape → Lifestyle: City Exploration (≤0.59 suggestion).
- Beach Retreat requires a natural beach context: sand OR shoreline OR beach umbrellas/chairs OR waves/sea/ocean/dunes. 
  Pools, tiled decks, cabanas, and patios ≠ Beach Retreat.

- Waterfront requires a natural water body (sea/ocean/lake/river/harbor/shoreline/pier/boats). 
  Exclude man-made water features (pool, hot tub, fountain).

- Pool / Spa at villa/house/hotel → map to Wellness: Swimming 
  (or Wellness: Self-care & Pamper if spa-like cues appear: "Spa", "Jacuzzi/Hot tub", "Massage", "Towels/Robes", "Candles").

- If Pool/Swimming pool AND Villa/House/Hotel/Resort/Patio/Terrace AND NO Sand/Shoreline/Waves/Sea/Ocean → 
  DO NOT output Beach Retreat or Waterfront.

- Architecture only when the building/structure is the **primary subject** (facade, cathedral, bridge, skyline) 
  AND leisure/pool scene does not dominate. If seating, deck, pool, umbrellas, or lounging cues are foregrounded → 
  suppress Architecture (cap ≤0.49 or suggest ≤0.59 max).

- Priority when pool+villa scene: Swimming or Self-care & Pamper > Architecture > generic Nature/Outdoors.


# --- Optional quick hints (kept tiny) ---
- "Amusement Park"/"Roller Coaster" → Entertainment: Theme Parks (≥0.85 if clear).
- "Billboard/Neon/Signage" + streetscape → Lifestyle: City Exploration (≤0.59 suggestion).
- "Pool" → Wellness: Swimming (or Self-care & Pamper if spa-like).

Scoring & Output:
- Cap generic scene tags ("Nature","Outdoors","Night") at ≤0.49 when any explicit ride/sport/urban cue exists.
- If a reasonable try to give suggested tag as well is you find any relevant.
- Return at least 1 and at most {top_k} 'matched' tags, and at least 1 and at most {top_k} 'suggested' tags (if relevant tags exist; otherwise, return none).
- Output valid JSON **only** (no prose):
{{
  "matched": [{{"tag":"Passion","category":"Category","score":0.90,"reasoning":"why"}}],
  "suggested": [{{"tag":"Passion","category":"Category","score":0.70,"reasoning":"why"}}]
}}

- Score bands: ≥0.80 strong, 0.60–0.79 good, ≤0.59 weak.
- Prefer precision over recall.
"""



def parse_llm_response(text: str) -> Dict[str, Any]:
    text = text.strip()
    if text.startswith("```"):
        text = text.split("```")[1].strip()
        if text.startswith("json"):
            text = text[4:].strip()
    try:
        data = json.loads(text)
    except Exception:
        return {"matched": [], "suggested": [], "error": "Invalid JSON"}
    return data
def _parse_taxonomy(passions_text: str):
    # "Category: tag1, tag2, ..." per line
    parents, tag_to_parent = set(), {}
    for line in passions_text.splitlines():
        if ":" not in line: 
            continue
        parent, tags = line.split(":", 1)
        parent = parent.strip()
        parents.add(parent)
        for t in [x.strip() for x in tags.split(",") if x.strip()]:
            tag_to_parent[t] = parent
    return parents, tag_to_parent

PARENTS, TAG2PARENT = _parse_taxonomy(PASSIONS_TEXT)


def enforce_whitelist(payload: dict):
    def fix(items):
        out = []
        for it in items:
            tag = it.get("tag")
            if tag in TAG2PARENT:
                it["category"] = TAG2PARENT[tag]  # force correct parent
                out.append(it)
        return out
    payload["matched"] = fix(payload.get("matched", []))
    payload["suggested"] = fix(payload.get("suggested", []))
    return payload


class LLMPassionMatcher:
    def __init__(self, config: MatcherConfig):
        self.cfg = config
        self.cache = LruTtlCache(config.cache_capacity, config.cache_ttl_seconds)
        genai.configure(api_key=config.api_key)
        self.model = genai.GenerativeModel(config.model_name)
    def match(self, labels, top_k=6):
        key = stable_cache_key(labels, top_k)
        if cached := self.cache.get(key):
            return {"cached": True, **cached}
        prompt = build_prompt(labels, top_k)
        for attempt in range(self.cfg.max_retries):
            try:
                resp = self.model.generate_content(
                    prompt,
                    generation_config={
                        "temperature": self.cfg.temperature,
                        "top_p": self.cfg.top_p,
                        "max_output_tokens": self.cfg.max_output_tokens,
                        
                    },
                )
                result = parse_llm_response(resp.text)
                result = enforce_whitelist(result)
                self.cache.set(key, result)
                return {"cached": False, **result}
            except Exception as e:
                print(f"Retry {attempt+1}: {e}")
                time.sleep(min(2**attempt, 8) + random.random())
        return {"matched": [], "suggested": [], "error": "LLM request failed"}

if __name__ == "__main__":
    cfg = MatcherConfig(api_key=API_KEY)
    matcher = LLMPassionMatcher(cfg)
    start_time = time.time()

    print("Running Passion Matcher test...\n")
    result = matcher.match(AWS_LABELS, TOP_K)
    print(json.dumps(result, indent=2))
    
    end_time = time.time()
    elapsed = end_time - start_time
    print(f"\nTime taken: {elapsed:.2f} seconds")

