## Application Configuration
APP_NAME=Marriott Pseudo Microservice
DEBUG=false
ENVIRONMENT=development
SECRET_KEY=your-super-secret-key-change-in-production

# Service Ports
AUTH_SERVICE_PORT=8010
IMAGES_SERVICE_PORT=8011
TAGS_SERVICE_PORT=8012
ADMIN_SERVICE_PORT=8013

# JWT Configuration
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1

# DynamoDB Configuration
DYNAMODB_TABLE_PREFIX=marriott
DYNAMODB_ENDPOINT_URL=  # Leave empty for AWS, set for local DynamoDB

# S3 Configuration
S3_BUCKET_NAME=marriott-images

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=20

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Sentry Configuration (optional)
SENTRY_DSN=  # Your Sentry DSN
SENTRY_ENVIRONMENT=development

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Images Service Configuration
MAX_IMAGE_SIZE_MB=10
ALLOWED_IMAGE_TYPES=["image/jpeg","image/png","image/webp"]

# Tags Service Configuration
MAX_TAGS_PER_IMAGE=20
REKOGNITION_CONFIDENCE_THRESHOLD=80.0

## Admin Service Configuration
ADMIN_API_KEY=admin-api-key-change-in-production
