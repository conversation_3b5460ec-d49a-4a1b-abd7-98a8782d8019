"""Recommendation service functions."""

import logging
from datetime import UTC, datetime
from typing import Any

from app.core.database import get_db_service

logger = logging.getLogger(__name__)


async def create_recommendations(
    user_id: str, batch_id: str, all_matched_tags: list
) -> dict[str, Any]:
    """
    Create and save recommendations based on aggregated tags.
    
    Args:
        user_id: User ID
        batch_id: Batch ID
        all_matched_tags: List of all matched tags from all images
        
    Returns:
        Created recommendation record
    """
    try:
        db = get_db_service()
        
        # Aggregate tags by parent category
        category_aggregation = {}
        
        for tag in all_matched_tags:
            parent_cat = tag["parent_cat"]
            marriott_tag = tag["marriott_tag"]
            score = tag["score"]
            
            if parent_cat not in category_aggregation:
                category_aggregation[parent_cat] = {}
            
            if marriott_tag not in category_aggregation[parent_cat]:
                category_aggregation[parent_cat][marriott_tag] = {
                    "count": 0,
                    "total_score": 0.0,
                    "max_score": 0.0,
                }
            
            cat_tag = category_aggregation[parent_cat][marriott_tag]
            cat_tag["count"] += 1
            cat_tag["total_score"] += score
            cat_tag["max_score"] = max(cat_tag["max_score"], score)
        
        # Build recommendation categories
        recommendation_categories = []
        for parent_cat, tags_data in category_aggregation.items():
            # Sort tags by relevance (count * average score)
            sorted_tags = sorted(
                tags_data.items(),
                key=lambda x: x[1]["count"] * (x[1]["total_score"] / x[1]["count"]),
                reverse=True
            )
            
            # Take top tags for this category
            top_tags = [tag_name for tag_name, _ in sorted_tags[:5]]  # Top 5 tags per category
            
            if top_tags:
                # Get hotel recommendations for this category
                hotel_recommendations = await get_hotel_recommendations(parent_cat, top_tags)
                
                recommendation_categories.append({
                    "parent_cat": parent_cat,
                    "tags": top_tags,
                    "recommendations": hotel_recommendations,
                })
        
        # Create RECOMMENDATION record
        now = datetime.now(UTC).isoformat()
        recommendation_item = {
            "PK": f"USER#{user_id}",
            "SK": f"RECOMMENDATION#{batch_id}",
            "batch_id": batch_id,
            "created_at": now,
            "categories": recommendation_categories,
        }
        
        # Save to database
        db.table.put_item(Item=recommendation_item)
        
        logger.info(f"Created recommendations for batch {batch_id}: {len(recommendation_categories)} categories")
        return recommendation_item
        
    except Exception as e:
        logger.error(f"Failed to create recommendations for batch {batch_id}: {e}")
        raise


async def get_hotel_recommendations(parent_cat: str, tags: list[str]) -> list[dict[str, Any]]:
    """
    Get hotel recommendations for a category and tags.
    
    This is a mock implementation. In a real system, this would query
    a hotel database or recommendation engine.
    
    Args:
        parent_cat: Parent category (e.g., "Outdoors", "Luxury")
        tags: List of relevant tags
        
    Returns:
        List of hotel recommendations
    """
    # Mock hotel data based on category
    mock_hotels = {
        "Outdoors": [
            {
                "hotel_id": "outdoor-resort-001",
                "title": "Mountain Adventure Resort",
                "score": 95,
                "description": "Perfect for outdoor enthusiasts",
            },
            {
                "hotel_id": "outdoor-resort-002", 
                "title": "Wilderness Lodge & Spa",
                "score": 92,
                "description": "Luxury meets nature",
            },
        ],
        "Luxury": [
            {
                "hotel_id": "luxury-hotel-001",
                "title": "Grand Palace Hotel",
                "score": 98,
                "description": "Ultimate luxury experience",
            },
            {
                "hotel_id": "luxury-hotel-002",
                "title": "Royal Suites Resort",
                "score": 96,
                "description": "Sophisticated elegance",
            },
        ],
        "Beach": [
            {
                "hotel_id": "beach-resort-001",
                "title": "Oceanfront Paradise Resort",
                "score": 94,
                "description": "Beachfront luxury",
            },
            {
                "hotel_id": "beach-resort-002",
                "title": "Tropical Bay Hotel",
                "score": 91,
                "description": "Island getaway",
            },
        ],
    }
    
    # Get recommendations for the category, or default ones
    recommendations = mock_hotels.get(parent_cat, [
        {
            "hotel_id": f"{parent_cat.lower()}-hotel-001",
            "title": f"{parent_cat} Resort & Spa",
            "score": 90,
            "description": f"Great for {parent_cat.lower()} experiences",
        }
    ])
    
    # Adjust scores based on tag relevance (mock logic)
    for rec in recommendations:
        # Boost score if tags are highly relevant
        if len(tags) > 3:
            rec["score"] = min(100, rec["score"] + 2)
        
        # Add tag context
        rec["relevant_tags"] = tags[:3]  # Top 3 tags
    
    logger.debug(f"Generated {len(recommendations)} hotel recommendations for {parent_cat}")
    return recommendations


async def get_user_recommendations(user_id: str, batch_id: str) -> dict[str, Any] | None:
    """
    Get existing recommendations for a user and batch.
    
    Args:
        user_id: User ID
        batch_id: Batch ID
        
    Returns:
        Recommendation record or None if not found
    """
    try:
        db = get_db_service()
        
        response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"RECOMMENDATION#{batch_id}"}
        )
        
        return response.get("Item")
        
    except Exception as e:
        logger.error(f"Failed to get recommendations for batch {batch_id}: {e}")
        raise


async def get_user_all_recommendations(user_id: str, limit: int = 10) -> list[dict[str, Any]]:
    """
    Get all recommendations for a user.
    
    Args:
        user_id: User ID
        limit: Maximum number of recommendations to return
        
    Returns:
        List of recommendation records
    """
    try:
        db = get_db_service()
        
        response = db.table.query(
            KeyConditionExpression="PK = :pk AND begins_with(SK, :sk_prefix)",
            ExpressionAttributeValues={
                ":pk": f"USER#{user_id}",
                ":sk_prefix": "RECOMMENDATION#",
            },
            ScanIndexForward=False,  # Most recent first
            Limit=limit,
        )
        
        return response.get("Items", [])
        
    except Exception as e:
        logger.error(f"Failed to get user recommendations: {e}")
        raise
