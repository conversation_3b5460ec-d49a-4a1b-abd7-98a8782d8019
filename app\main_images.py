"""Images Service - Fora-Marriott Pseudo Microservice."""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core import get_settings, init_sentry, setup_logging
from app.images import router as images_router

# Get settings
settings = get_settings()

# Setup logging and monitoring
setup_logging("images")
init_sentry("images")

# Create FastAPI application
app = FastAPI(
    title=f"{settings.app_name} - Images Service",
    description="Image management and storage microservice",
    version=settings.version,
    debug=settings.debug,
    openapi_prefix="/images",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Include images routes
app.include_router(images_router)
