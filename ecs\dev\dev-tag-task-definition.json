{"family": "fora-marriott-dev-tag-api-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-dev-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-dev-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-dev-tag-api", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-dev:tag-api-dev-latest", "essential": true, "cpu": 256, "memory": 512, "portMappings": [{"appProtocol": "http", "containerPort": 8003, "hostPort": 8003, "protocol": "tcp"}, {"containerPort": 50053, "hostPort": 50053, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tag-service-fora-marriott-dev", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "dev"}, {"name": "MAX_CONNECTIONS", "value": "50000"}]}], "volumes": []}