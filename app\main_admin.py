"""Admin Service - Fora-Marriott Pseudo Microservice."""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.admin import router as admin_router
from app.core import get_settings, init_sentry, setup_logging

# Get settings
settings = get_settings()

# Setup logging and monitoring
setup_logging("admin")
init_sentry("admin")

# Create FastAPI application
app = FastAPI(
    title=f"{settings.app_name} - Admin Service",
    description="Administrative and management microservice",
    version=settings.version,
    debug=settings.debug,
    openapi_prefix="/admin",
    # docs_url="/admin/docs",
    # openapi_url="/admin/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Include admin routes
app.include_router(admin_router)
