"""Images service routes."""

import ssl
from datetime import UTC, datetime
from typing import Annotated

import redis
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from app.core import get_settings
from app.core.auth import TokenData, get_current_user
# Import celery app for task dispatch
from app.core.celery_app import celery, images_dummy_count
from app.core.database import get_db_service
from app.core.decorators import response_handler
from app.core.redis_service import get_redis_service
from app.services.images import check_user_image_limit
from app.services.models.common import CommonResponse
from app.services.models.image import UploadInitRequest, UploadInitResponse
from app.services.repository.image_repo import ImageService, get_image_service
from app.services.utilities.s3_helper import S3Service, get_s3_service

router = APIRouter()
settings = get_settings()


UserTokenData = Annotated[TokenData, Depends(get_current_user)]


@router.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for images service."""
    return {
        "status": "ok",
        "timestamp": datetime.now(UTC).isoformat(),
        "service": "images",
        "version": settings.version,
        "environment": settings.environment,
    }


@router.get("/")
async def root() -> dict[str, str]:
    """Root endpoint for images service."""
    return {
        "message": "Fora-Marriott Images Service",
        "version": settings.version,
        "docs": "/docs",
        "health": "/health",
    }


@router.get("/protected")
@response_handler
async def protected_images_endpoint(
    current_user: UserTokenData,
) -> dict:
    """Protected endpoint - requires valid JWT token."""
    return {
        "message": "Access granted to Images service",
        "user_id": current_user.user_id,
        "cust_id": current_user.cust_id,
        "service": "images",
    }


@router.get("/images")
@response_handler
async def list_user_images(
    current_user: UserTokenData,
) -> dict:
    """List images for authenticated user."""
    return {
        "message": f"Images for user {current_user.user_id}",
        "user_id": current_user.user_id,
        "images": [],  # TODO: Implement actual image listing
    }


@router.post("/uploads/init", response_model=CommonResponse[UploadInitResponse])
@response_handler
async def upload_init(
    request: UploadInitRequest,
    current_user: UserTokenData,
    upload_service: Annotated[ImageService, Depends(get_image_service)],
    s3_service: Annotated[S3Service, Depends(get_s3_service)],
) -> UploadInitResponse:
    """
    Initialize upload batch and generate S3 presigned URLs.

    Frontend will call this API with image names to get presigned URLs
    for direct S3 upload.
    """
    # Validate image names
    if not request.image_names:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one image name is required",
        )
    
    
    # Check batch limit (max 5 per request)
    if len(request.image_names) > 5:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You can upload a maximum of 5 images per batch"
        )

    
    user_id = str(current_user.user_id)
    
    existing_image_count = await check_user_image_limit(user_id)
    total_images_after_processing = existing_image_count 

    # Check total limit (25 images max)
    if total_images_after_processing > 25:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"User image limit exceeded: {existing_image_count} existing total (max 25 images)"
        )
    

    return await upload_service.generate_signed_url(
        user_id=str(current_user.user_id), request=request, s3_service=s3_service
    )


@router.post(
    "/uploads/{batch_id}/start",
)
@response_handler
async def start_processing(batch_id: str, current_user: UserTokenData) -> dict:
    """
    Start image processing for a batch after images are uploaded to S3.

    Limits:
    - Maximum 5 images per batch
    - Maximum 25 images total per user (5 batches × 5 images)

    This endpoint triggers the Celery task pipeline:
    1. process_batch_task - orchestrates the processing
    2. process_image_task - processes each image with Rekognition
    3. match_tags_task - matches AWS labels with Marriott tags
    4. aggregate_recommendations_task - creates final recommendations
    """
    try:
        user_id = str(current_user.user_id)

        # Get database service
      
    

        # 3. All checks passed - trigger the batch processing pipeline
        task_result = celery.send_task(
            "images.process_batch", args=[user_id, batch_id]
        )

        return {
            "status": "PROCESSING",
            "batch_id": batch_id,
            "task_id": task_result.id,
            "message": "Image processing started",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start processing: {str(e)}",
        )


@router.get("/uploads/{batch_id}/status")
@response_handler
async def get_batch_status(batch_id: str, current_user: UserTokenData) -> dict:
    """
    Get the processing status of a batch.

    Returns batch status, progress, and any recommendations if completed.
    """
    try:
        from app.core.database import get_db_service

        db = get_db_service()
        user_id = str(current_user.user_id)

        # Get batch status
        batch_response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"}
        )

        if "Item" not in batch_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Batch {batch_id} not found",
            )

        batch_item = batch_response["Item"]

        result = {
            "batch_id": batch_id,
            "status": batch_item.get("status", "UNKNOWN"),
            "progress": batch_item.get("progress", {}),
            "created_at": batch_item.get("created_at"),
            "processing_started_at": batch_item.get("processing_started_at"),
            "processing_completed_at": batch_item.get("processing_completed_at"),
            "last_error": batch_item.get("last_error"),
        }

        # If completed, include recommendations
        if batch_item.get("status") == "COMPLETED":
            rec_response = db.table.get_item(
                Key={"PK": f"USER#{user_id}", "SK": f"RECOMMENDATION#{batch_id}"}
            )

            if "Item" in rec_response:
                result["recommendations"] = rec_response["Item"].get("categories", [])

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get batch status: {str(e)}",
        )

@router.post(
    "/generate",
)
@response_handler
async def generate_recommendations(current_user: UserTokenData) -> dict:
    """
    Trigger recommendation generation for the current user.

    This enqueues the Celery task:
    1. generate_user_recommendations_task - aggregates all matched tags,
       finds associated content, and stores recommendations in DynamoDB.
    """
    try:
        user_id = str(current_user.user_id)

        task_result = celery.send_task(
            "images.generate_user_recommendations", args=[user_id]
        )

        return {
            "status": "PROCESSING",
            "user_id": user_id,
            "task_id": task_result.id,
            "message": "Recommendation generation started",
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start recommendation generation: {str(e)}",
        )


@router.get("/infra/status", include_in_schema=True)
async def infra_status():
    """
    Check if Celery worker and Redis are online, and show broker/backend URLs.
    """
    settings = get_settings()

    # === Celery Worker Check ===
    try:
        inspector = celery.control.inspect(timeout=1.0)
        ping = inspector.ping()
        celery_ok = bool(ping)
        celery_workers = list(ping.keys()) if ping else []
    except Exception as e:
        celery_ok = False
        celery_workers = []
        celery_error = str(e)
    else:
        celery_error = None

    # === Redis Check (Primary) ===
    try:
        r = redis.Redis.from_url(settings.redis_url)
        r.ping()
        redis_ok = True
        redis_error = None
    except Exception as e:
        redis_ok = False
        redis_error = str(e)

    # === Broker URL Check ===
    try:
        broker_client = redis.Redis.from_url(settings.redis_url)
        broker_client.ping()
        broker_status = "reachable"
    except Exception as e:
        broker_status = f"error: {str(e)}"

    # === Backend URL Check ===
    try:
        backend_client = redis.Redis.from_url(settings.redis_backend_url)
        backend_client.ping()
        backend_status = "reachable"
    except Exception as e:
        backend_status = f"error: {str(e)}"

    return JSONResponse(
        content={
            "celery_ok": celery_ok,
            "celery_workers": celery_workers,
            "celery_error": celery_error,
            "redis_ok": redis_ok,
            "redis_error": redis_error,
            "celery_broker_url": settings.redis_url,
            "celery_backend_url": settings.redis_backend_url,
            "broker_status": broker_status,
            "backend_status": backend_status,
        }
    )


@router.get("/redis-test", include_in_schema=True)
async def redis_test():
    """
    Test Redis connectivity, key set/get, and broker/backend URL reachability.
    """
    try:
        settings = get_settings()

        # === Active Redis client ===
        client = get_redis_service().get_client()
        active_url = settings.redis_url  # or however your service resolves it

        # === Ping + Key test ===
        pong = client.ping()
        test_key = "redis_test_key"
        test_value = "ok test key value working fine"
        client.set(test_key, test_value, ex=10)
        value = client.get(test_key)

        # === Dynamic Redis URLs ===
        broker_url = f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/0"
        backend_url = f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/1"

        # === Broker test ===
        try:
            broker_client = redis.Redis.from_url(
                broker_url,
                decode_responses=True,
                ssl=True,
                ssl_cert_reqs=ssl.CERT_NONE
            )
            broker_status = "reachable" if broker_client.ping() else "unreachable"
        except Exception as e:
            broker_status = f"error: {str(e)}"

        # === Backend test ===
        try:
            backend_client = redis.Redis.from_url(
                backend_url,
                decode_responses=True,
                ssl=True,
                ssl_cert_reqs=ssl.CERT_NONE
            )
            backend_status = "reachable" if backend_client.ping() else "unreachable"
        except Exception as e:
            backend_status = f"error: {str(e)}"

        return {
            "redis_status": "ok" if pong else "fail",
            "ping": pong,
            "test_key": test_key,
            "test_value": value.decode() if value else None,
            "message": "Redis ping and key set/get successful" if pong and value else "Redis test failed",
            "active_redis_url": active_url,
            "broker_url": broker_url,
            "broker_status": broker_status,
            "backend_url": backend_url,
            "backend_status": backend_status,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis connection failed: {e}")

def test_redis_url(url: str) -> str:
    try:
        pool = redis.ConnectionPool.from_url(
            url,
            ssl_cert_reqs=ssl.CERT_NONE  # Disable cert validation for AWS ElastiCache TLS
        )
        client = redis.Redis(connection_pool=pool, decode_responses=True)
        client.ping()
        return "reachable"
    except Exception as e:
        return f"error: {str(e)}"

@router.get("/infra/redis-check", include_in_schema=True)
async def redis_check():
    """
    Check if Redis broker and backend URLs are reachable before Celery starts.
    """
    settings = get_settings()

    broker_url = f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/0"
    backend_url = f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/1"

    broker_status = test_redis_url(broker_url)
    backend_status = test_redis_url(backend_url)

    return JSONResponse(
        content={
            "broker_url": broker_url,
            "broker_status": broker_status,
            "backend_url": backend_url,
            "backend_status": backend_status,
            "message": "Redis connectivity check complete"
        }
    )

    
@router.post("/redis-set", include_in_schema=True)
async def redis_set(key: str, value: str):
    """
    Set a key/value in Redis.
    """
    try:
        client = get_redis_service().get_client()
        client.set(key, value, ex=3600)
        return {"status": "ok", "message": f"Key '{key}' set with value '{value}'"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis set failed: {e}")

@router.get("/redis-get", include_in_schema=True)
async def redis_get(key: str):
    """
    Get a value by key from Redis.
    """
    try:
        client = get_redis_service().get_client()
        value = client.get(key)
        return {"status": "ok", "key": key, "value": value.decode() if value else None}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis get failed: {e}")

@router.get("/redis-keys", include_in_schema=True)
async def redis_keys(pattern: str = "*"):
    """
    List all keys in Redis (optionally filter by pattern).
    """
    try:
        client = get_redis_service().get_client()
        keys = [k.decode() for k in client.keys(pattern)]
        return {"status": "ok", "keys": keys}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis keys failed: {e}")
    
@router.delete("/redis-keys/remove", include_in_schema=True)
async def remove_redis_key(key: str = "*"):
    """
    Delete a specific Redis key or all keys matching a pattern.
    If no key is specified, deletes all keys.
    """
    try:
        client = get_redis_service().get_client()

        # If wildcard or no key specified, delete all matching keys
        if key == "*" or "*" in key:
            keys = client.keys(key)
            if not keys:
                return {"status": "ok", "deleted": 0, "message": "No matching keys found"}
            deleted_count = client.delete(*keys)
            return {
                "status": "ok",
                "deleted": deleted_count,
                "message": f"Deleted {deleted_count} keys matching pattern '{key}'"
            }

        # Otherwise, delete specific key
        deleted = client.delete(key)
        if deleted == 0:
            return {"status": "ok", "deleted": 0, "message": f"Key '{key}' not found"}
        return {"status": "ok", "deleted": 1, "message": f"Key '{key}' deleted successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis key deletion failed: {e}")    