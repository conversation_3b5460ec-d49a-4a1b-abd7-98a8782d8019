"""
Simple image analysis service for POC testing.
Handles AWS Rekognition + Gemini AI integration.
"""

import base64
import json
import re
import time
from typing import Any, Dict, List

import boto3
from botocore.exceptions import ClientError
from google import genai
from google.genai import types
from typing_extensions import TypedDict

from app.core.config import get_settings


class SimpleImageAnalysisService:
    """Lightweight service for image analysis POC."""
    
    def __init__(self):
        """Initialize the service."""
        self.settings = get_settings()
        self.gemini_api_key = self.settings.gemini_api_key
        self.passions = [
                        {"tag": "Hike", "parent": "Outdoors"},
                        {"tag": "Wildlife", "parent": "Outdoors"},
                        {"tag": "Hike & Glamp", "parent": "Outdoors"},
                        {"tag": "Safari", "parent": "Outdoors"},
                        {"tag": "Ski & Snowboard", "parent": "Outdoors"},
                        {"tag": "Bike", "parent": "Outdoors"},
                        {"tag": "Trail Running", "parent": "Outdoors"},
                        {"tag": "Rock Climbing", "parent": "Outdoors"},
                        {"tag": "Fish", "parent": "Outdoors"},
                        {"tag": "Scuba & Snorkel", "parent": "Outdoors"},
                        {"tag": "Kayak & Canoe", "parent": "Outdoors"},
                        {"tag": "Surf", "parent": "Outdoors"},
                        {"tag": "Golf", "parent": "Active"},
                        {"tag": "Tennis", "parent": "Active"},
                        {"tag": "Running", "parent": "Active"},
                        {"tag": "Swimming", "parent": "Active"},
                        {"tag": "Water Sports", "parent": "Active"},
                        {"tag": "Restaurants", "parent": "Culinary"},
                        {"tag": "Food Trends", "parent": "Culinary"},
                        {"tag": "Fine Dining", "parent": "Culinary"},
                        {"tag": "Local Cuisine", "parent": "Culinary"},
                        {"tag": "Cooking & Baking", "parent": "Culinary"},
                        {"tag": "Wine", "parent": "Culinary"},
                        {"tag": "Beer", "parent": "Culinary"},
                        {"tag": "Spirits", "parent": "Culinary"},
                        {"tag": "Coffee", "parent": "Culinary"},
                        {"tag": "Mixology", "parent": "Culinary"},
                        {"tag": "Fitness & Exercise", "parent": "Wellness"},
                        {"tag": "Yoga", "parent": "Wellness"},
                        {"tag": "Medical", "parent": "Wellness"},
                        {"tag": "Mindfulness", "parent": "Wellness"},
                        {"tag": "Healthy Food", "parent": "Wellness"},
                        {"tag": "Self-care & Spa", "parent": "Wellness"},
                        {"tag": "Home & Garden", "parent": "Wellness"},
                        {"tag": "Sleep", "parent": "Wellness"},
                        {"tag": "Kids & Grandkids", "parent": "Community"},
                        {"tag": "Business & Finance", "parent": "Community"},
                        {"tag": "Skill Building", "parent": "Community"},
                        {"tag": "Professional Development", "parent": "Community"},
                        {"tag": "Emerging Tech", "parent": "Community"},
                        {"tag": "Environmental", "parent": "Community"},
                        {"tag": "Community Service", "parent": "Community"},
                        {"tag": "Pets", "parent": "Community"},
                        {"tag": "Friends", "parent": "Community"},
                        {"tag": "Football", "parent": "Entertainment"},
                        {"tag": "Formula 1", "parent": "Entertainment"},
                        {"tag": "Soccer", "parent": "Entertainment"},
                        {"tag": "Tennis", "parent": "Entertainment"},
                        {"tag": "Basketball", "parent": "Entertainment"},
                        {"tag": "Baseball", "parent": "Entertainment"},
                        {"tag": "Winter Sports", "parent": "Entertainment"},
                        {"tag": "Golf", "parent": "Entertainment"},
                        {"tag": "Music", "parent": "Entertainment"},
                        {"tag": "Artist Fandom", "parent": "Entertainment"},
                        {"tag": "Festivals & Concerts", "parent": "Entertainment"},
                        {"tag": "Show Fandom", "parent": "Entertainment"},
                        {"tag": "Movie Genres", "parent": "Entertainment"},
                        {"tag": "Hollywood Culture", "parent": "Entertainment"},
                        {"tag": "Animation & Fandom", "parent": "Entertainment"},
                        {"tag": "Gambling", "parent": "Entertainment"},
                        {"tag": "Video Games", "parent": "Entertainment"},
                        {"tag": "Cultures", "parent": "Culture"},
                        {"tag": "Historical Events", "parent": "Culture"},
                        {"tag": "Politics & Current Events", "parent": "Culture"},
                        {"tag": "Landmarks", "parent": "Culture"},
                        {"tag": "Art & Photography", "parent": "Culture"},
                        {"tag": "Theater", "parent": "Culture"},
                        {"tag": "Architecture", "parent": "Culture"},
                        {"tag": "Literature", "parent": "Culture"},
                        {"tag": "Creation", "parent": "Culture"},
                        {"tag": "STEM", "parent": "Culture"},
                        {"tag": "Fashion", "parent": "Culture"},
                        {"tag": "Design", "parent": "Culture"},
                        {"tag": "Shopping", "parent": "Culture"},
                    ]
    
    def _setup_aws_rekognition(self) -> boto3.  client:
        """Setup AWS Rekognition client."""
        aws_config = {"region_name": self.settings.aws_region}
        
        if self.settings.s3_access_key_id and self.settings.s3_secret_access_key:
            aws_config["aws_access_key_id"] = self.settings.s3_access_key_id
            aws_config["aws_secret_access_key"] = self.settings.s3_secret_access_key
            
        return boto3.client("rekognition", **aws_config)
    
    def _analyze_with_rekognition(self, image_bytes: bytes) -> List[Dict[str, Any]]:
        """Analyze image with AWS Rekognition."""
        try:
            rekognition_client = self._setup_aws_rekognition()
            original_labels_to_exclude = [
                                "Adult", "Alien", "Angel", "Angler", "Ankle", "Archer", "Arm", "Baby", "Back", "Barefoot", "Beard",
                                "Body Part", "Boy", "Bride", "Bridegroom", "Bridesmaid", "Bullfighter", "Child", "Crowd", "Cupid",
                                "Dimples", "Ear", "Elf", "Face", "Family", "Female", "Finger", "Fist", "Freckle", "Girl", "Hairdresser",
                                "Hand", "Head", "Heart", "Heel", "Hip", "Hippie", "Jaw", "Jury", "Knee", "Lady", "Male", "Man",
                                "Military Officer", "Monk", "Mouth", "Mustache", "Navel", "Neck", "Newborn", "Pedestrian", "People",
                                "Person", "Senior Citizen", "Shoulder", "Skeleton", "Skin", "Stomach", "Student", "Team", "Teen",
                                "Teeth", "Thigh", "Throat", "Toe", "Tongue", "Torso", "Tourist", "Tribe", "Troop", "Veins", "Woman", "Wrist"
                            ]

            include_labels = [
                    "Baby", "Boy", "Child", "Family", "Hairdresser", "Girl", "Jury", "Monk", "Senior Citizen", "Student", "Team", "Teen", "Person"
                ]

            aws_rek_exclude_label_list = [label for label in original_labels_to_exclude if label not in include_labels]
            
            response = rekognition_client.detect_labels(
                Image={'Bytes': image_bytes},
                MaxLabels=30,
                MinConfidence=float(self.settings.rekognition_confidence_threshold or 70.0),
                Settings={
                    "GeneralLabels": {
                        "LabelCategoryExclusionFilters": [
                            "Damage Detection",
                            "Everyday Objects",
                            "Home Appliances",
                            "Patterns and Shapes",
                            "Materials",
                            # "Offices and Workspaces",
                            # "Person Description",
                            "Furniture and Furnishings",
                            "Public Safety",
                            "Religion",
                            "Tools and Machinery",
                            "Weapons and Military",
                        ],
                        "LabelExclusionFilters":aws_rek_exclude_label_list
                    }
                }
            )
            
            aws_labels = []
            for label in response.get('Labels', []):
                aws_labels.append({
                    "label": label['Name'],
                    "score": round(label['Confidence'], 2)
                })
            
            return aws_labels
            
        except ClientError as e:
            raise Exception(f"AWS Rekognition error: {str(e)}")
        except Exception as e:
            raise Exception(f"Image processing error: {str(e)}")
    
    def _analyze_with_gemini_old(self, aws_labels: List[Dict[str, Any]]) -> tuple[str, List[str]]:
        """Analyze labels with Gemini AI to find matching passions."""
        try:
            genai.configure(api_key=self.gemini_api_key)
            model = genai.GenerativeModel("models/gemini-2.5-flash")
            
            # Create prompt for Gemini
            label_names = [label["label"] for label in aws_labels]
            prompt = f"""
Based on these image labels: {label_names}

From this passion list: {self.passions}

Return up to 3 most relevant passions that match the image content.
Respond with only a JSON array of passion names, like: ["Hike", "Wildlife", "Photography"]
"""
            
            # Get Gemini response
            response = model.generate_content(prompt)
            gemini_result = response.text.strip()
            
            # Try to extract JSON from response
            final_passions = []
            try:
                json_match = re.search(r'\[.*?\]', gemini_result)
                if json_match:
                    final_passions = json.loads(json_match.group())
            except:
                final_passions = []
            
            return gemini_result, final_passions
            
        except Exception as e:
            error_msg = f"Gemini AI error: {str(e)}"
            return error_msg, []
    
    
    def _analyze_with_gemini(self, aws_labels: List[Dict[str, Any]]) -> tuple[str, List[str]]:
        """
        Analyze AWS Rekognition labels with Gemini AI to find matching passions.
        Uses the new google-genai SDK (JSON mode + response schema).
        """
        try:
            client = genai.Client(api_key=self.gemini_api_key)
            REQUIRE_AT_LEAST_ONE_MATCH = True   
            PROMOTE_CAP = 0.59  # cap score for weak promotions

            # Extract label names
            label_names = [label.get("label", "") for label in aws_labels]

            # Compose the structured prompt
            prompt = f"""
    You are an AI that maps image labels to user passions for a travel/lifestyle product.

    Important contextual expectations:
    - Beach + Person does NOT imply "Water Sports" unless labels like "Surf", "Surfboard", "Kayak", or "Canoe" appear.
    - "Piano" without "Stage" maps to "Music", not "Theater".
    - "Horse" + "Person" and no "Wildlife" implies "Horseback Riding" (if not present in the catalog, you may still suggest it).
    - "Child" + "Family" ⇒ "Kids & Grandkids" when available.
    - Woods/Forest with a person likely indicates "Hike"; with "Camera/Tripod" it leans "Art & Photography".
    - A generic "Person" alone does not imply fitness.
    - Do NOT match “Ski & Snowboard” from generic “Snow/Mountain” alone. Require explicit cues like “Ski”, “Skiing”, “Snowboard”, or visible gear. 
    If only “Snow/Mountain”, treat it as a low-confidence suggestion (≤0.59).
    - Do NOT match “Wildlife” unless labels include an animal (e.g., “Deer”, “Bird”, “Bear”, “Horse (wild)”, etc.). 
    “Woods + Camera/Tripod” without animals ⇒ “Art & Photography” (and possibly “Hike”), not “Wildlife”.
    - Prefer “Hike & Glamp” over plain “Hike” when “Tent” appears with “Mountain/Backpack”.
    - When multiple top items are very similar, prefer diversity across parents unless the evidence is overwhelmingly for one parent.
    -Always include at least one item in "matched" if any reasonable candidate exists. If you would otherwise return only suggestions, promote the single best candidate into "matched".
    -If the promoted item lacks explicit activity cues, cap its score at ≤ 0.59 and keep the reason concise.
    -Only return empty matched and suggested when there is truly no reasonable candidate.
    -Never invent activities not implied by labels; prefer broad, low-confidence passions (e.g., “Art & Photography”, “Hike”) over specific sports when evidence is weak.

    Return JSON only, following this schema:
    {{
    "matched": [{{"tag": str, "parent": str, "score": float, "why": str}}],
    "suggested": [{{"tag": str, "parent": str, "score": float, "why": str}}]
    }}

    Score 0.0–1.0. ≥0.80 = strong, 0.60–0.79 = good, ≤0.59 = weak.
    Prefer precision over recall.

    Labels: {label_names}
    Catalog: {self.passions}
    """

            # JSON schema for structured response
            MATCHER_RESPONSE_SCHEMA = {
                "type": "object",
                "properties": {
                    "matched": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "tag": {"type": "string"},
                                "parent": {"type": "string"},
                                "score": {"type": "number"},
                                "why": {"type": "string"},
                            },
                            "required": ["tag", "parent", "score", "why"],
                        },
                    },
                    "suggested": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "tag": {"type": "string"},
                                "parent": {"type": "string"},
                                "score": {"type": "number"},
                                "why": {"type": "string"},
                            },
                            "required": ["tag", "parent", "score", "why"],
                        },
                    },
                },
                "required": ["matched", "suggested"],
            }

            start = time.time()

            # Call Gemini with structured JSON output
            response = client.models.generate_content(
                model="gemini-2.5-pro",
                contents=prompt,
                config=types.GenerateContentConfig(
                    response_mime_type="application/json",
                    response_schema=MATCHER_RESPONSE_SCHEMA,
                    temperature=0.2,
                    top_p=0.95,
                    top_k=40,
                ),
            )

            # Prefer parsed JSON, fallback to text
            result = getattr(response, "parsed", None)
            if result is None:
                result = json.loads(response.text or "{}")
            
            if REQUIRE_AT_LEAST_ONE_MATCH:
                matched = result.get("matched", [])
                suggested = result.get("suggested", [])
                if not matched and suggested:
                    # Promote best suggestion to matched
                    best = suggested.pop(0)  # they come sorted by score; if not, sort first
                    # Cap score if it’s a weak evidence case (no explicit activity)
                    try:
                        best["score"] = float(best.get("score", 0.55))
                    except Exception:
                        best["score"] = 0.55
                    if best["score"] > PROMOTE_CAP:
                        best["score"] = PROMOTE_CAP
                    # Tag rationale so it’s auditable (optional)
                    best["why"] = (best.get("why", "") + " [promoted from suggested due to fallback]").strip()
                    result.setdefault("matched", []).insert(0, best)
                    result["suggested"] = suggested  # write back    

            # Extract the top 3 matched passion names
            # final_passions = [m["tag"] for m in result.get("matched", [])][:3]

            # Add diagnostics (optional)
            result["_diagnostics"] = {"llm_time_sec": round(time.time() - start, 2)}

            return result

        except Exception as e:
            error_msg = f"Gemini AI error: {type(e).__name__}: {str(e)}"
            return error_msg, []
    
    
    async def analyze_image(self, image_bytes: bytes, image_name: str = "uploaded_image") -> Dict[str, Any]:
        """
        Main method to analyze an image.
        
        Args:
            image_bytes: Raw image bytes
            image_name: Optional name for the image
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Step 1: Analyze with AWS Rekognition
            aws_labels = self._analyze_with_rekognition(image_bytes)
            
            # Step 2: Analyze with Gemini AI
            gemini_result = self._analyze_with_gemini(aws_labels)
            
            return {
                "image_name": image_name,
                "aws_labels": aws_labels,
                "gemini_result": gemini_result,
                # "final_passions": final_passions,
                "total_aws_labels": len(aws_labels),
                "success": True
            }
            
        except Exception as e:
            return {
                "image_name": image_name,
                "error": str(e),
                "aws_labels": [],
                "gemini_result": "",
                # "final_passions": [],
                "total_aws_labels": 0,
                "success": False
            }


# Dependency injection
def get_simple_image_analysis_service() -> SimpleImageAnalysisService:
    """Get simple image analysis service instance."""
    return SimpleImageAnalysisService()
