"""S3 service for file upload operations."""

import boto3
from botocore.exceptions import ClientError
from sentry_sdk import capture_exception

from app.core.config import get_settings


class S3Service:
    """Service for S3 operations."""

    def __init__(self) -> None:
        """Initialize S3 service."""
        self.settings = get_settings()
        self.bucket_name = self.settings.s3_bucket_name
        self.region = self.settings.aws_region

        # Always use default credentials (IAM role, environment, etc.)
        # self.s3_client = boto3.client("s3", region_name=self.region)

        # Initialize S3 client
        if self.settings.s3_access_key_id and self.settings.s3_secret_access_key:
            self.s3_client = boto3.client(
                "s3",
                region_name=self.region,
                aws_access_key_id=self.settings.s3_access_key_id,
                aws_secret_access_key=self.settings.s3_secret_access_key,
            )
        else:
            # Use default credentials (IAM role, etc.)
            self.s3_client = boto3.client("s3", region_name=self.region)

    def generate_presigned_url(
        self, s3_key: str, expiration: int = 3600, content_type: str = "image/jpeg"
    ) -> str:
        """Generate presigned URL for S3 upload."""
        try:
            # For testing/mock mode, return a mock URL
            if self.settings.s3_mock_mode:
                return f"https://mock-s3-url.com/{self.bucket_name}/{s3_key}?mock=true"

            # Generate real presigned URL
            url = self.s3_client.generate_presigned_url(
                "put_object",
                Params={
                    "Bucket": self.bucket_name,
                    "Key": s3_key,
                    "ContentType": content_type,
                    "Tagging": "auto-expire=true"

                },
                ExpiresIn=expiration,
            )
            return url

        except ClientError as e:
            print(f"Error generating presigned URL: {e}")
            capture_exception(
                e,
                extra_data={"image_s3_key": s3_key},
            )
            # return f"https://mock-s3-url.com/{self.bucket_name}/{s3_key}?error=true"
            return None

    def generate_multiple_presigned_urls(
        self, s3_keys: list[str], expiration: int = 3600
    ) -> list[dict[str, str]]:
        """Generate multiple presigned URLs."""
        urls = []

        for s3_key in s3_keys:
            # Determine content type based on extension
            extension = s3_key.split(".")[-1].lower()
            content_type = self._get_content_type(extension)

            url = self.generate_presigned_url(s3_key, expiration, content_type)
            urls.append({"key": s3_key, "url": url})

        return urls

    def _get_content_type(self, extension: str) -> str:
        """Get content type based on file extension."""
        content_types = {
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "webp": "image/webp",
            "heic": "image/heic",
            "heif": "image/heif",
        }
        return content_types.get(extension, "application/octet-stream")

    def validate_file_extension(self, filename: str) -> bool:
        """Validate if file extension is allowed."""
        allowed_extensions = {"jpg", "jpeg", "png", "gif", "webp", "heic", "heif"}
        extension = filename.split(".")[-1].lower() if "." in filename else ""
        return extension in allowed_extensions


# Global service instance
_s3_service: S3Service | None = None


def get_s3_service() -> S3Service:
    """Get the global S3 service instance."""
    global _s3_service
    if _s3_service is None:
        _s3_service = S3Service()
    return _s3_service
