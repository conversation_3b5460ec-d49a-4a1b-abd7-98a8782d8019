#!/bin/bash
echo "Starting All Fora-Marriott Microservices..."
echo
echo "Services will be available at:"
echo "  Auth Service:   http://localhost:8010/docs"
echo "  Images Service: http://localhost:8011/docs"
echo "  Tags Service:   http://localhost:8012/docs"
echo "  Admin Service:  http://localhost:8013/docs"
echo
echo "Press Ctrl+C to stop all services"
echo

# Add uv to PATH if needed
export PATH="$HOME/.local/bin:$PATH"

# Start services in background
./scripts/run-auth.sh &
sleep 2
./scripts/run-images.sh &
sleep 2
./scripts/run-tags.sh &
sleep 2
./scripts/run-admin.sh &

echo "All services started!"
echo "Check the individual terminal windows for service logs."

# Wait for user input to stop
read -p "Press Enter to stop all services..."

# Kill all background jobs
jobs -p | xargs kill
