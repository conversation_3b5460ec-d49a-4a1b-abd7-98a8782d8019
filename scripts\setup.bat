@echo off
echo Fora-Marriott Microservice Setup Script
echo ==================================
echo.

echo Step 1: Installing uv package manager...
@REM powershell -Command "irm https://astral.sh/uv/install.ps1 | iex"
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
if %errorlevel% neq 0 (
    echo Failed to install uv. Please install manually from: https://docs.astral.sh/uv/
    pause
    exit /b 1
)

echo.
echo Step 2: Installing project dependencies...
set PATH=%USERPROFILE%\.local\bin;%PATH%
uv sync
if %errorlevel% neq 0 (
    echo Failed to install dependencies.
    pause
    exit /b 1
)

echo.
echo Step 3: Running tests to verify setup...
uv run pytest tests/ -v
if %errorlevel% neq 0 (
    echo Tests failed. Please check the setup.
    pause
    exit /b 1
)

echo.
echo ✅ Setup completed successfully!
echo.
echo Quick start commands:
echo   scripts\run-auth.bat     - Start Auth service
echo   scripts\run-all.bat      - Start all services
echo   uv run pytest           - Run tests
echo   uv run ruff check .      - Lint code
echo.
pause
