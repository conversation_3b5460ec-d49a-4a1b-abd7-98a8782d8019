# Fora-Marriott Pseudo Microservice Architecture.  

A modern, scalable pseudo-microservice architecture built with **FastAPI**, **uv**, **ruff**, and **mypy**. Designed for independent deployment to AWS ECS with LocalStack support for local development .

### Services

| Service | Port | Purpose | Docker Image |
|---------|------|---------|--------------|
| **Auth** | 8010 | Authentication & Authorization | `marriott-auth` |
| **Images** | 8011 | Image Management & Storage | `marriott-images` |
| **Tags** | 8012 | Image Tagging & Analysis | `marriott-tags` |
| **Admin** | 8013 | Administrative Operations | `marriott-admin` |

## Technology Stack

- ** FastAPI 0.116+** - Modern async web framework
- ** uv** - Ultra-fast Python package manager
- ** Ruff** - Lightning-fast linting and formatting
- ** MyPy** - Static type checking
- ** Docker** - Containerization for each service
- ** LocalStack** - Local AWS services simulation
- ** Pytest** - Modern testing framework

## Quick Start for Windows 11

### Prerequisites
- **Windows 11**
- **Python 3.11+** (Download from [python.org](https://python.org))
- **Git** (Download from [git-scm.com](https://git-scm.com))
- **Docker Desktop** (Optional, for containerized development)

### 1. Automated Setup (Recommended)
```cmd
# Clone the repository
git clone <your-repo-url>
cd fora-marriott-backend

# Enviornment Variable
copy .env.sample .env

# Run the automated setup script
scripts\setup.bat
```

### 2. Manual Setup
```cmd
# Step 1: Install uv package manager
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Step 2: Install dependencies
uv sync

# Step 3: Verify setup
uv run pytest tests/ -v
```

### 3. Running Services (Windows)

#### Option A: Individual Services
```cmd
# Start individual services (each in separate terminal)
scripts\run-auth.bat     # http://localhost:8010/docs
scripts\run-images.bat   # http://localhost:8011/docs
scripts\run-tags.bat     # http://localhost:8012/docs
scripts\run-admin.bat    # http://localhost:8013/docs
```

#### Option B: All Services at Once
```cmd
# Start all services in separate windows
scripts\run-all.bat
```

#### Option C: Manual Commands
```cmd
# Add uv to PATH (if needed)
set PATH=%USERPROFILE%\.local\bin;%PATH%

# Start services manually
uv run fastapi dev app/main_auth.py --port 8010
uv run fastapi dev app/main_images.py --port 8011
uv run fastapi dev app/main_tags.py --port 8012
uv run fastapi dev app/main_admin.py --port 8013
```

### 4. Docker Development (Optional)

```cmd
# Build all service images
docker-compose build

# Start all services with Docker
docker-compose up -d

# Stop all services
docker-compose down
```

## Project Structure

```
fora-marriott-backend/
├── app/                    # Application code
│   ├── core/              # Shared utilities
│   │   ├── __init__.py
│   │   ├── config.py      # Pydantic settings with LocalStack support
│   │   └── logger.py      # JSON logger scaffold
│   ├── auth/              # Auth service module
│   │   ├── __init__.py
│   │   └── routes.py      # Auth routes (health + TODO endpoints)
│   ├── images/            # Images service module
│   │   ├── __init__.py
│   │   └── routes.py      # Images routes (health + TODO endpoints)
│   ├── tags/              # Tags service module
│   │   ├── __init__.py
│   │   └── routes.py      # Tags routes (health + TODO endpoints)
│   ├── admin/             # Admin service module
│   │   ├── __init__.py
│   │   └── routes.py      # Admin routes (health + TODO endpoints)
│   ├── main_auth.py       # Auth service FastAPI app
│   ├── main_images.py     # Images service FastAPI app
│   ├── main_tags.py       # Tags service FastAPI app
│   ├── main_admin.py      # Admin service FastAPI app
│   └── __init__.py
├── scripts/               # Windows batch scripts
│   ├── setup.bat          # Automated setup script
│   ├── run-auth.bat       # Start auth service
│   ├── run-images.bat     # Start images service
│   ├── run-tags.bat       # Start tags service
│   ├── run-admin.bat      # Start admin service
│   └── run-all.bat        # Start all services
├── tests/                 # Test files
├── Dockerfile.auth        # Auth service Docker image
├── Dockerfile.images      # Images service Docker image
├── Dockerfile.tags        # Tags service Docker image
├── Dockerfile.admin       # Admin service Docker image
├── docker-compose.yml     # Multi-service orchestration
├── docker-compose.localstack.yml  # LocalStack setup
├── pyproject.toml         # uv, ruff, mypy configuration
├── .pre-commit-config.yaml # Pre-commit hooks
├── .editorconfig          # Editor configuration
├── .env                   # Environment variables
├── .env.sample            # Environment template
└── README.md              # This file
```

## Configuration

### Environment Variables

Key configuration options in `.env`:

## Development Commands (Windows)

### Package Management
```cmd
# Install dependencies
uv sync

# Add new dependency
uv add fastapi-users

# Add dev dependency
uv add --dev pytest-mock
```

### Code Quality
```cmd
# Lint code
uv run ruff check .

# Format code
uv run ruff format .

# Type checking
uv run mypy app/

# Run tests
uv run pytest tests/ -v

# Run all quality checks
uv run ruff check . && uv run ruff format . && uv run mypy app/ && uv run pytest
```

### Service Management
```cmd
# Individual services (Windows batch scripts)
scripts\run-auth.bat      # Auth service
scripts\run-images.bat    # Images service
scripts\run-tags.bat      # Tags service
scripts\run-admin.bat     # Admin service

# All services at once
scripts\run-all.bat

# Manual service start
uv run fastapi dev app/main_auth.py --port 8010
```

### Health Checks
```cmd
# Check individual service health
curl http://localhost:8010/health  # Auth
curl http://localhost:8011/health  # Images
curl http://localhost:8012/health  # Tags
curl http://localhost:8013/health  # Admin

# Or use PowerShell
Invoke-RestMethod -Uri "http://localhost:8010/health" -Method GET
```
## API Documentation

When services are running, access interactive API docs:

- **Auth Service**: http://localhost:8010/docs
- **Images Service**: http://localhost:8011/docs
- **Tags Service**: http://localhost:8012/docs
- **Admin Service**: http://localhost:8013/docs

## Monitoring & Health Checks

All services provide standardized health endpoints:

```bash
# Check individual service health
curl http://localhost:8010/health

# Check all services
make health
```

Response format:
```json
{
  "status": "ok",
  "timestamp": "2025-09-04T11:20:18.333402",
  "service": "auth",
  "version": "1.0.0",
  "environment": "development"
}
```
**Built with ❤️ for scalable microservice architecture** 


