# app/core/celery_app.py
import logging
import time

from celery import Celery
from celery.signals import worker_ready, worker_shutdown
from kombu import Exchange, Queue

from app.core import get_settings

# Get settings
settings = get_settings()

# Build secure Redis URLs.

if settings.environment == "local":
    broker_url = settings.redis_backend_url
    backend_url = settings.redis_backend_url
else:
    broker_url = (
            f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/0"
            "?ssl_cert_reqs=CERT_NONE"
        )
    backend_url = (
            f"rediss://:{settings.redis_password}@{settings.redis_host}:{settings.redis_port}/1"
            "?ssl_cert_reqs=CERT_NONE"
    )


# broker=settings.redis_backend_url,
# backend=settings.redis_backend_url,

# Initialize Celery app.
celery = Celery(
    # "fora-marriott_workers",
     __name__,
    broker=broker_url,
    backend=backend_url,
    include=[
        "app.images.tasks",
        "app.tags.tasks",
       
    ],
)

celery.conf.update(
    worker_pool_restarts=True,
    broker_connection_retry_on_startup=True,
    result_expires=3600,
    # Other Celery settings...
)

logger = logging.getLogger("celery_startup")
logger.setLevel(logging.INFO)

@worker_ready.connect
def startup_info(sender, **kwargs):
    logger.info("✅ Celery worker is ready.")
    logger.info(f"🔗 Redis broker/backend URL: {broker_url , backend_url}")
    logger.info(f"📦 App name: {celery.main}")
    logger.info(f"🕒 Result expires in: {celery.conf.result_expires} seconds")
    print("✅ Celery worker is ready.")
    print(f"🔗 Redis broker/backend URL: {broker_url, backend_url}")
    print(f"📦 App name: {celery.main}")
    print(f"🕒 Result expires in: {celery.conf.result_expires} seconds")

@worker_shutdown.connect
def shutdown_info(sender, **kwargs):
    logger.info("🛑 Celery worker is shutting down.")
    logger.info("📤 All pending tasks will be revoked or retried based on config.")
    print("🛑 Celery worker is shutting down.")
    print("📤 All pending tasks will be revoked or retried based on config.")


@celery.task(name="images.dummy_count")
def images_dummy_count(n: int = 1000) -> dict:
    """
    Simple CPU-bound-ish loop to measure worker processing time.
    Returns duration and sum result (safety) .
    """
    start = time.perf_counter()
    s = 0
    for i in range(1, n + 1):
        s += i
    duration = time.perf_counter() - start
    return {"service": "images", "n": n, "sum": s, "duration_seconds": duration}



# Define per-service queues (explicit)
# celery.conf.task_queues = (
#     # Queue("images", Exchange("images"), routing_key="images"),
#     Queue("tags", Exchange("tags"), routing_key="tags"),
#     # Queue("auth", Exchange("auth"), routing_key="auth"),
# )

# Route tasks by name pattern to queues
# celery.conf.task_routes = {
#     # "images.*": {"queue": "images", "routing_key": "images"},
#     "tags.*": {"queue": "tags", "routing_key": "tags"},
#     # "auth.*": {"queue": "auth", "routing_key": "auth"},
# }

# Common sensible defaults
# celery.conf.update(
#     task_serializer="json",
#     accept_content=["json"],
#     result_serializer="json",
#     task_track_started=True,
#     worker_prefetch_multiplier=1,
#     task_acks_late=True,
#     # consider acks_late True for safety on long-running tasks
# )

# from app.images.tasks import (aggregate_recommendations_task,
#                               generate_user_recommendations_task, images_dummy_count,
#                               match_tags_task, process_batch_task, process_image_task)
# # Import tasks to ensure they are registered
# from app.tags.tasks import tags_dummy_io
