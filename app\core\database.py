"""DynamoDB database operations."""

import boto3
from botocore.exceptions import Client<PERSON><PERSON><PERSON>

from .config import get_settings


class DynamoDBService:
    """DynamoDB service for operations."""

    def __init__(self):
        """Initialize DynamoDB service."""
        self.settings = get_settings()
        self.table_name = self.settings.dynamodb_table_name
        
        # Initialize DynamoDB client using default credentials IAM role
        dynamodb_args = {
            "region_name": self.settings.aws_region
        }

        # Use local endpoint if explicitly provided (e.g., for local testing)
        if self.settings.dynamodb_endpoint_url:
            dynamodb_args["endpoint_url"] = self.settings.dynamodb_endpoint_url
            dynamodb_args["aws_access_key_id"] = "test"
            dynamodb_args["aws_secret_access_key"] = "test"


        self.dynamodb = boto3.resource("dynamodb", **dynamodb_args)
        self.table = self.dynamodb.Table(self.table_name)


        # Initialize DynamoDB client
        # Use local endpoint if explicitly provided, otherwise default to AWS
        # dynamodb_args = {
        #     "region_name": self.settings.aws_region,
        #     "aws_access_key_id": self.settings.dynamodb_access_key_id,
        #     "aws_secret_access_key": self.settings.dynamodb_secret_access_key,
        # }

        # if self.settings.dynamodb_endpoint_url:           
        #     dynamodb_args["endpoint_url"] = self.settings.dynamodb_endpoint_url

        # self.dynamodb = boto3.resource("dynamodb", **dynamodb_args)
        # self.table = self.dynamodb.Table(self.table_name)

        # self.table = self.dynamodb.Table(self.table_name)

    async def ensure_table_exists(self) -> bool:
        """Ensure the DynamoDB table exists."""
        try:
            self.table.load()
            return True
        except ClientError:
            # Table doesn't exist, create it
            try:
                table = self.dynamodb.create_table(
                    TableName=self.table_name,
                    KeySchema=[
                        {"AttributeName": "PK", "KeyType": "HASH"},
                        {"AttributeName": "SK", "KeyType": "RANGE"},
                    ],
                    AttributeDefinitions=[
                        {"AttributeName": "PK", "AttributeType": "S"},
                        {"AttributeName": "SK", "AttributeType": "S"},
                    ],
                    BillingMode="PAY_PER_REQUEST",
                )

                # Wait for table to be created
                table.wait_until_exists()
                return True

            except ClientError as e:
                print(f"Error creating table: {e}")
                return False


# Global database service instance
_db_service: DynamoDBService | None = None


def get_db_service() -> DynamoDBService:
    """Get the global database service instance."""
    global _db_service
    if _db_service is None:
        _db_service = DynamoDBService()
    return _db_service
