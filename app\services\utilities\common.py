import re
import unicodedata


def normalize_token(s: str) -> str:
    """Normalize a string to a stable token for PK/SK suffix."""
        
    # Normalize options
    USE_UNDERSCORE = True  # True -> use '_' as separator, False -> use '-'
    KEEP_AMPERSAND = False  # False -> replace '&' with '_and_'; True -> keep '&'

    if s is None:
        return ""
    s0 = str(s).strip().lower()
    s0 = "".join(
        ch for ch in unicodedata.normalize("NFKD", s0) if not unicodedata.combining(ch)
    )

    if not KEEP_AMPERSAND:
        s0 = s0.replace("&", " and ")

    # allow alnum, space, dash, underscore, dot, (ampersand optionally)
    if KEEP_AMPERSAND:
        allowed = r"[^a-z0-9\s\-\_\&\.]"
    else:
        allowed = r"[^a-z0-9\s\-\_\.]"
    s0 = re.sub(allowed, "", s0)

    sep = "_" if USE_UNDERSCORE else "-"
    s0 = re.sub(r"\s+", sep, s0).strip(sep)

    # if we replaced '&' with ' and ', convert to _and_
    if not KEEP_AMPERSAND:
        # convert any leftover ' and ' patterns to _and_ (and collapse separators)
        s0 = re.sub(r"\band\b", "and", s0)
        s0 = (
            s0.replace(" and ", "_and_")
            .replace(" and_", "_and_")
            .replace("_and ", "_and_")
        )
        s0 = re.sub(r"\s+", sep, s0)
        s0 = s0.strip(sep)

    return s0