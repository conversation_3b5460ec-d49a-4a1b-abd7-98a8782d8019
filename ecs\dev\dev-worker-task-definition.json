{"family": "fora-marriott-dev-worker-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-dev-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-dev-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-dev-worker", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-dev:worker-api-dev-latest", "essential": true, "cpu": 512, "memory": 1024, "portMappings": [{"appProtocol": "http", "containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/worker-service-fora-marriott-dev", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "dev"}, {"name": "MAX_CONNECTIONS", "value": "50000"}], "command": ["sh", "-c", "celery -A app.core.celery_app worker --loglevel=INFO --concurrency=8 --prefetch-multiplier=4 --max-tasks-per-child=1000"]}], "volumes": []}