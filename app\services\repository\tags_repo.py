"""Tags repository for database operations."""

import asyncio
from typing import Any, Dict, List, Optional, Set

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from fastapi import HTTPException, status

from app.core.database import get_db_service
from app.services.utilities.common import normalize_token


class TagsRepository:
    """Repository for tags-related database operations."""

    def __init__(self) -> None:
        """Initialize tags repository."""
        self.db = get_db_service()

    def scan_all(self, table, **scan_kwargs):
        """Return a dict with an 'Items' key for compatibility."""
        items = []
        resp = table.scan(**scan_kwargs)
        items.extend(resp.get("Items", []))
        while resp.get("LastEvaluatedKey"):
            resp = table.scan(ExclusiveStartKey=resp["LastEvaluatedKey"], **scan_kwargs)
            items.extend(resp.get("Items", []))
        return {"Items": items}


    async def get_user_image_tags(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all image tags for a user."""
        try:
            # response = self.db.table.query(
            #     FilterExpression="begins_with(PK, :user_pk) AND begins_with(SK, :image_sk)",
            #     ExpressionAttributeValues={
            #         ":user_pk": f"USER#{user_id}",
            #         ":image_sk": "IMAGE#"
            #     }
            # )
            response = self.db.table.query(
                    KeyConditionExpression=Key("PK").eq(f"USER#{user_id}") & Key("SK").begins_with("IMAGE#")
                )

            
            images = response.get("Items", [])
            result = []
            
            for image in images:
                image_id = image.get('image_id')
                s3_key = image.get('s3_key')
                matched_tags = image.get('matched_tags', [])
                suggested_tags_raw = image.get('suggested_tags', [])
                aws_labels = image.get('aws_labels')
                
                
                if not image_id:
                    continue
                
                # Group tags by parent_cat
                tags_by_category = {}
                
                # Process matched_tags
                for tag in matched_tags:
                    parent_cat = tag.get('parent_cat')
                    marriott_tag = tag.get('marriott_tag')      
                    
                    if parent_cat and marriott_tag:
                        if parent_cat not in tags_by_category:
                            tags_by_category[parent_cat] = {
                                "parent_cat": parent_cat,
                                "tags": []


                            }
                        if marriott_tag not in tags_by_category[parent_cat]["tags"]:
                            tags_by_category[parent_cat]["tags"].append(marriott_tag)
                
                # Process suggested_tags
                print(suggested_tags_raw)
                # Extract distinct marriott_tags from suggested_tags
                seen = set()
                suggested_tags_list = []
                for tag_raw in suggested_tags_raw:
                    marriott_tag = tag_raw.get('marriott_tag')
                    if marriott_tag and marriott_tag not in seen:
                        seen.add(marriott_tag)
                        suggested_tags_list.append(marriott_tag)



     
                
                # Convert to list format
                tags_list = list(tags_by_category.values())

                
                result.append({
                    "image_key": s3_key,
                    "image_id": image_id,
                    "tags": tags_list,
                    "suggested_tags": suggested_tags_list,
                    "aws_labels": aws_labels,
                    "matched_tags_raw": matched_tags,
                    "suggested_tags_raw": suggested_tags_raw

                })
            
            return result
            
        except ClientError as e:
            print(f"Error fetching user image tags: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to fetch user image tags: {str(e)}"
            )

    async def update_image_tags(self, user_id: str, image_updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Update tags for multiple images."""
        try:
            updated_count = 0
            failed_updates = []

            for update in image_updates:
                image_id = update.get('image_id')
                new_tags = update.get('tags', [])

                # Validate image_id
                if not image_id or image_id == "string":
                    failed_updates.append({
                        "image_id": image_id,
                        "reason": "Invalid or dummy image_id"
                    })
                    continue

                # Validate tags
                if not new_tags or (len(new_tags) == 1 and new_tags[0] == "string"):
                    failed_updates.append({
                        "image_id": image_id,
                        "reason": "Invalid or dummy tags"
                    })
                    continue

                # Get current image record
                response = self.db.table.get_item(
                    Key={
                        "PK": f"USER#{user_id}",
                        "SK": f"IMAGE#{image_id}"
                    }
                )

                if "Item" not in response:
                    failed_updates.append({
                        "image_id": image_id,
                        "reason": "Image not found"
                    })
                    continue

                image_item = response["Item"]
                current_matched_tags = image_item.get('matched_tags', [])
                current_suggested_tags = image_item.get('suggested_tags', [])

                # Update matched_tags - replace with new tags
                updated_matched_tags = []

                for tag_name in new_tags:
                    # Try to find the tag in current matched_tags to get parent_cat
                    parent_cat = None
                    for existing_tag in current_matched_tags:
                        if existing_tag.get('marriott_tag') == tag_name:
                            parent_cat = existing_tag.get('parent_cat')
                            break

                    # If not found in matched tags, try suggested tags
                    if not parent_cat:
                        for existing_tag in current_suggested_tags:
                            if existing_tag.get('marriott_tag') == tag_name:
                                parent_cat = existing_tag.get('parent_cat')
                                break

                    # If still not found, try to find in master tags
                    if not parent_cat:
                        parent_cat = await self._find_parent_cat_for_tag(tag_name)

                    if parent_cat:
                        updated_matched_tags.append({
                            "marriott_tag": tag_name,
                            "parent_cat": parent_cat
                        })
                    else:
                        # Tag not found in any source
                        failed_updates.append({
                            "image_id": image_id,
                            "tag": tag_name,
                            "reason": "Tag not found in master tags"
                        })

                # Only update if we have valid tags
                if updated_matched_tags:
                    self.db.table.update_item(
                        Key={
                            "PK": f"USER#{user_id}",
                            "SK": f"IMAGE#{image_id}"
                        },
                        UpdateExpression="SET matched_tags = :tags",
                        ExpressionAttributeValues={
                            ":tags": updated_matched_tags
                        }
                    )
                    updated_count += 1
                else:
                    # Only add a generic failure if we haven't already logged tag-level failures
                    if not any(f.get("image_id") == image_id for f in failed_updates):
                        failed_updates.append({
                            "image_id": image_id,
                            "reason": "No valid tags to update"
                        })

            return {
                "updated_count": updated_count,
                "failed_updates": failed_updates,
                "total_requested": len(image_updates)
            }

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update image tags: {str(e)}"
            )

    async def _find_parent_cat_for_tag(self, marriott_tag: str) -> Optional[str]:
        """Find parent category for a marriott tag from master tags."""
        try:
            # Scan master tags to find the parent category
            # response = self.db.table.scan(
            #     FilterExpression="begins_with(PK, :master_pk) AND begins_with(SK, :tag_sk) AND marriott_tag = :tag",
            #     ExpressionAttributeValues={
            #         ":master_pk": "MASTER_TAG#",
            #         ":tag_sk": "TAG#",
            #         ":tag": marriott_tag
            #     }
            # )
            tag_sk = f"TAG#{normalize_token(marriott_tag)}"
        
            response = self.db.table.query(
                IndexName='SK_index',  # Use the new index
                KeyConditionExpression='SK = :sk',
                ExpressionAttributeValues={
                    ':sk': tag_sk
                }
            )
            
            items = response.get("Items", [])
            if items:
                return items[0].get('parent_cat')
            
            return None
            
        except ClientError:
            return None

    async def get_all_master_tags_old(self) -> List[Dict[str, Any]]:
        """Get all master tags grouped by parent category."""
        try:
            # First get all parent categories
            parent_response = self.scan_all(
                self.db.table,
                FilterExpression="begins_with(PK, :master_pk) AND begins_with(SK, :parent_sk)",
                ExpressionAttributeValues={
                    ":master_pk": "MASTER_TAG#",
                    ":parent_sk": "PARENT#"
                }
            )
            
            # Then get all tags
            tags_response = self.scan_all(
                self.db.table,
                FilterExpression="begins_with(PK, :master_pk) AND begins_with(SK, :tag_sk)",
                ExpressionAttributeValues={
                    ":master_pk": "MASTER_TAG#",
                    ":tag_sk": "TAG#"
                }
            )
            
            # Group by parent category
            parents_by_cat = {}
            for parent in parent_response.get("Items", []):
                parent_cat = parent.get('parent_cat')
                if parent_cat:
                    parents_by_cat[parent_cat] = {
                        "parent_cat": parent_cat,
                        "parent_image": parent.get('parent_image'),
                        "parent_desc": parent.get('parent_desc'),
                        "tags": []
                    }
            
            # Add tags to their parent categories
            for tag in tags_response.get("Items", []):
                parent_cat = tag.get('parent_cat')
                marriott_tag = tag.get('marriott_tag')
                activity_image = tag.get('activity_image')
                active = tag.get('active', True)
                
                if parent_cat and marriott_tag and active:
                    if parent_cat in parents_by_cat:
                        parents_by_cat[parent_cat]["tags"].append({
                            "marriott_tag": marriott_tag,
                            "activity_image": activity_image
                        })
            
            # Convert to list and filter out categories with no tags
                    # Convert to list and filter out categories with no tags
            result = [parent_data for parent_data in parents_by_cat.values() if parent_data["tags"]]
            # result = []
            # for parent_data in parents_by_cat.values():
            #     if parent_data["tags"]:  # Only include categories that have tags
            #         result.append(parent_data)
            
            
            return result
            
        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to fetch master tags: {str(e)}"
            )

    async def get_all_master_tags(self) -> List[Dict[str, Any]]:
        """Get all master tags grouped by parent category (no table scans)."""
        try:
            # Optional: wrap .query() calls in asyncio.to_thread() if you ever need better concurrency.
            
            # resp = await asyncio.to_thread(
            #         self.db.table.query,
            #         KeyConditionExpression=Key("PK").eq(pk_val) & Key("SK").begins_with("TAG#")
            #     )

            # 1) Query parents via GSI
            parents_resp = self.db.table.query(
                IndexName="GSI1",
                KeyConditionExpression=Key("GSI1PK").eq("MASTER_PARENT")
            )
            parent_items = parents_resp.get("Items", [])

            # Build parent skeletons keyed by normalized parent (GSI1SK or PK suffix)
            parents_by_cat = {}
            for parent in parent_items:
                parent_cat = parent.get("parent_cat")               # display name "Culinary"
                normalized = parent.get("GSI1SK") or parent.get("PK").split("#")[-1]
                parents_by_cat[normalized] = {
                    "parent_cat": parent_cat,
                    "parent_image": parent.get("parent_image"),
                    "parent_desc": parent.get("parent_desc"),
                    "priority": parent.get("priority", 9999),
                    "tags": []
                }

            # 2) For each parent, query tags by PK = MASTER_TAG#<normalized>, SK begins_with 'TAG#'
            async def fetch_tags_for_parent(normalized):
                pk_val = f"MASTER_TAG#{normalized}"
                resp =  self.db.table.query(
                    KeyConditionExpression=Key("PK").eq(pk_val) & Key("SK").begins_with("TAG#")
                )
                return normalized, resp.get("Items", [])

            # Launch queries concurrently
            fetch_tasks = [fetch_tags_for_parent(n) for n in parents_by_cat.keys()]
            results = await asyncio.gather(*fetch_tasks, return_exceptions=False)

            # Assign tags into parents_by_cat
            for normalized, tag_items in results:
                for tag in tag_items:
                    active = tag.get("active", True)
                    marriott_tag = tag.get("marriott_tag")
                    activity_image = tag.get("activity_image")
                    if active and marriott_tag:
                        parents_by_cat[normalized]["tags"].append({
                            "marriott_tag": marriott_tag,
                            "activity_image": activity_image
                        })

            # 3) Convert to list and filter empty categories
            result = [
                parent_data for parent_data in parents_by_cat.values() if parent_data["tags"]
            ]

            # 4) Sort result by priority ascending (1 is highest priority)
            result.sort(key=lambda x: x.get("priority", 9999))

            return result

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to fetch master tags: {str(e)}"
            )
    # Insert this method into your TagsRepository class
    async def get_user_recommendations(self, user_id: str) -> List[Dict]:
        """
        Fetch user's recommendation record, batch-get related CONTENT and MASTER_PARENT items,
        and return grouped response by parent category:
        [
            {
            "parent_cat": "...",
            "parent_desc": "...",
            "parent_icon": "...",
            "recommendations": [
                {"image": "...", "desc": "...", "title": "...", "link": "..."},
                ...
            ]
            },
            ...
        ]
        """
        try:
            # 1) fetch recommendation record (single item) using the Table resource get_item
            rec_key = {"PK": f"USER#{user_id}", "SK": f"RECOMMENDATION#{user_id}"}
            rec_resp = self.db.table.get_item(Key=rec_key)
            rec_item = rec_resp.get("Item")
            if not rec_item:
                return []

            categories = rec_item.get("categories", [])

            # 2) collect content ids (deduped per parent) and parent_pk set
            content_ids_per_parent: Dict[str, Set[str]] = {}
            parent_pk_set: Set[str] = set()

            for cat in categories:
                parent_cat = cat.get("parent_cat")
                parent_pk = cat.get("pk")  # e.g. MASTER_TAG#outdoors
                if parent_pk:
                    parent_pk_set.add(parent_pk)

                ids: Set[str] = set()
                for tag in cat.get("tags", []):
                    for cid in tag.get("content", []):
                        if cid:
                            ids.add(cid)

                if parent_cat and ids:
                    # If same parent_cat appears multiple times, unify ids
                    content_ids_per_parent.setdefault(parent_cat, set()).update(ids)

            # 3) build list of Keys to fetch (CONTENT items and PARENT#META items)
            keys_to_get: List[Dict] = []

            # CONTENT keys
            for ids in content_ids_per_parent.values():
                for cid in ids:
                    keys_to_get.append({"PK": f"CONTENT#{cid}", "SK": "DETAILS"})

            # PARENT meta keys
            for parent_pk in parent_pk_set:
                keys_to_get.append({"PK": parent_pk, "SK": "PARENT#META"})

            if not keys_to_get:
                return []
            # ✅ Deduplicate keys before batch_get_item to avoid ValidationException
            keys_to_get = [dict(t) for t in {tuple(sorted(d.items())) for d in keys_to_get}]

            # 4) batch_get_item with chunking (DynamoDB limit 100 keys per RequestItems table)
            table_name = self.db.table_name
            dynamodb_resource = self.db.dynamodb  # boto3.resource("dynamodb", ...)
            # get low-level client to call batch_get_item
            dynamodb_client = getattr(dynamodb_resource, "meta").client if hasattr(dynamodb_resource, "meta") else dynamodb_resource

            # chunk keys into groups of max 100
            def chunks(lst, n):
                for i in range(0, len(lst), n):
                    yield lst[i : i + n]

            responses_items = []
            for chunk in chunks(keys_to_get, 100):
                request_items = {table_name: {"Keys": chunk}}
                batch_resp = dynamodb_client.batch_get_item(RequestItems=request_items)
                # collect returned items
                batch_items = batch_resp.get("Responses", {}).get(table_name, [])
                responses_items.extend(batch_items)

                # handle UnprocessedKeys (simple retry once)
                unprocessed = batch_resp.get("UnprocessedKeys", {})
                if unprocessed:
                    unprocessed_keys = unprocessed.get(table_name, {}).get("Keys", [])
                    if unprocessed_keys:
                        retry_req = {table_name: {"Keys": unprocessed_keys}}
                        retry_resp = dynamodb_client.batch_get_item(RequestItems=retry_req)
                        retry_items = retry_resp.get("Responses", {}).get(table_name, [])
                        responses_items.extend(retry_items)
                        # if still unprocessed — ignore or log; we continue with what we have

            # 5) map results: content_map by content_id, parent_meta_map by parent_pk
            content_map: Dict[str, Dict] = {}
            parent_meta_map: Dict[str, Dict] = {}

            for item in responses_items:
                pk = item.get("PK", "")
                sk = item.get("SK", "")
                if pk.startswith("CONTENT#") and sk == "DETAILS":
                    # content_id may be in item["content_id"] or derived from PK
                    cid = item.get("content_id") or pk.replace("CONTENT#", "")
                    content_map[cid] = item
                elif sk == "PARENT#META" and pk.startswith("MASTER_TAG#"):
                    parent_meta_map[pk] = item

            # 6) build final grouped response
            result: List[Dict] = []
            for cat in categories:
                parent_cat = cat.get("parent_cat")
                parent_pk = cat.get("pk")

                # Pick parent meta if present
                parent_meta = parent_meta_map.get(parent_pk, {}) if parent_pk else {}
                parent_desc = parent_meta.get("parent_desc") or parent_meta.get("parent_desc")
                parent_icon = parent_meta.get("parent_icon")
                parent_image = parent_meta.get("parent_image")
                priority = parent_meta.get("priority")

                # fallback: construct icon from image (e.g. Culinary_passion.webp -> Culinary_passion_icon.webp)
                if not parent_icon and parent_image and isinstance(parent_image, str):
                    # simple logic: replace .webp suffix if present or append "_icon"
                    if parent_image.lower().endswith(".webp"):
                        parent_icon = parent_image[:-5] + "_icon.webp"
                    else:
                        parent_icon = f"{parent_image}_icon"

                # assemble unique recommendation list for this parent category
                ids = content_ids_per_parent.get(parent_cat, set())
                recommendations = []
                seen = set()
                for cid in ids:
                    if cid in seen:
                        continue
                    seen.add(cid)
                    c = content_map.get(cid)
                    if not c:
                        # if content item not found in batch results, skip gracefully
                        continue
                    recommendations.append(
                        {
                            "image": c.get("image"),
                            "desc": c.get("description"),
                            "title": c.get("title"),
                            "url": c.get("url"),
                            "priority": c.get("priority"),
                            "content_type": c.get("content_type"),
                        }
                    )

                if recommendations:
                    result.append(
                        {
                            "parent_cat": parent_cat,
                            "parent_desc": parent_desc,
                            "parent_icon": parent_icon,
                            "parent_priority": priority,
                            "recommendations": recommendations,
                        }
                    )

            return result

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"DynamoDB ClientError fetching recommendations: {str(e)}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error fetching recommendations: {str(e)}",
            )


    async def reset_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Delete all user images and recommendations."""
        try:
            deleted_count = 0

            # Query all items for this user
            response = self.db.table.query(
                KeyConditionExpression="PK = :pk",
                ExpressionAttributeValues={
                    ":pk": f"USER#{user_id}"
                }
            )

            items = response.get("Items", [])

            # Filter for IMAGE and RECOMMENDATION items
            items_to_delete = [
                item for item in items
                if item.get("SK", "").startswith("IMAGE#") or item.get("SK", "").startswith("RECOMMENDATION#")
            ]

            # Delete each item
            for item in items_to_delete:
                self.db.table.delete_item(
                    Key={
                        "PK": item["PK"],
                        "SK": item["SK"]
                    }
                )
                deleted_count += 1

            return {
                "success": True,
                "deleted_items": deleted_count,
                "message": f"Successfully deleted {deleted_count} items (images and recommendations)"
            }

        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to reset user profile: {str(e)}"
            )


def get_tags_repository() -> TagsRepository:
    """Get tags repository instance."""
    return TagsRepository()
