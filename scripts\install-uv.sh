#!/bin/bash
echo "Installing uv package manager..."
echo

# Install uv using curl
curl -LsSf https://astral.sh/uv/install.sh | sh

if [ $? -ne 0 ]; then
    echo "Failed to install uv automatically."
    echo "Please install manually from: https://docs.astral.sh/uv/getting-started/installation/"
    exit 1
fi

echo
echo "✅ uv installed successfully!"
echo

# Add uv to PATH for this session
export PATH="$HOME/.local/bin:$PATH"

echo "Testing uv installation..."
uv --version

if [ $? -ne 0 ]; then
    echo "❌ uv not found in PATH. Please add to your shell profile:"
    echo "export PATH=\"\$HOME/.local/bin:\$PATH\""
    exit 1
fi

echo
echo "✅ uv is ready to use!"
echo
echo "Next steps:"
echo "  1. Run: uv sync"
echo "  2. Run: ./scripts/run-auth.sh"
echo
