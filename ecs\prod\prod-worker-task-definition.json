{"family": "fora-marriott-prod-worker-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-prod-worker", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-prod:worker-api-prod-latest", "essential": true, "cpu": 1024, "memory": 2048, "portMappings": [{"appProtocol": "http", "containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}, {"appProtocol": "http", "containerPort": 5555, "hostPort": 5555, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/worker-service-fora-marriott-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "prod"}, {"name": "MAX_CONNECTIONS", "value": "50000"}], "command": ["sh", "-c", "celery -A app.core.celery_app worker --loglevel=INFO --concurrency=8 --prefetch-multiplier=4 --max-tasks-per-child=1000 & celery -A app.core.celery_app flower --port=5555 --basic-auth=marriote:JlPx9aGk5"]}], "volumes": []}