{"family": "fora-marriott-prod-image-api-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-prod-image-api", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-prod:image-api-prod-latest", "essential": true, "cpu": 2048, "memory": 4096, "portMappings": [{"appProtocol": "http", "containerPort": 8002, "hostPort": 8002, "protocol": "tcp"}, {"containerPort": 50052, "hostPort": 50052, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/image-service-fora-marriott-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "prod"}, {"name": "MAX_CONNECTIONS", "value": "50000"}]}], "volumes": []}