#!/usr/bin/env python3
"""
Start Celery worker for image processing.
"""

import subprocess
import sys

def main():
    """Start the Celery worker."""
    cmd = [
        sys.executable, "-m", "celery", 
        "-A", "app.core.celery_app", 
        "worker", 
        "--loglevel=info", 
        "--queues=images,tags", 
        "--pool=solo"
    ]
    
    print("Starting Celery worker...")
    print(f"Command: {' '.join(cmd)}")
    
    # Start the worker
    subprocess.run(cmd)

if __name__ == "__main__":
    main()
