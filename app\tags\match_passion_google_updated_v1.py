#!/usr/bin/env python3
"""
Simple local test for the LLM-based Passion Matcher.

Requirements:
  pip install google-generativeai
"""

import hashlib
import json
import os
import random
import threading
import time
from collections import OrderedDict
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from app.core.config import get_settings

# ==== CONFIGURATION ====
settings = get_settings()
API_KEY = settings.gemini_api_key  # <-- Put your real Gemini key here
MODEL_NAME = "models/gemini-2.0-flash" # You can change to gemini-1.5-pro or others
TOP_K = 3

# Example AWS Rekognition-style labels for a beach image
AWS_LABELS =[
                    {
                        "score": "0.9999017333984375",
                        "label": "Bag"
                    },
                    
                ]



# =======================

try:
    import google.generativeai as genai
except Exception:
    raise RuntimeError("You must install google-generativeai: pip install google-generativeai")


PASSIONS_TEXT = """
Outdoor: Wildlife, Hike & Glamp, Safari, Ski & Snowboard, Bike, Fish, Scuba & Snorkel, Kayak & Canoe, Surf, Nature, Paddleboarding
Lifestyle: Rock Climbing, Golf, Tennis, Running, Swimming, Water Sports, Beach Retreat, City Exploration, Sailing & Boating, Horse Riding, Waterfront, Mountains, Adventure Sports
Culinary: Restaurants, Foodie Trends, Fine Dining, Local Cuisine, Cooking & Baking, Wine, Beer, Spirits, Coffee, Mixology
Wellness: Cardio, Strength Training, Yoga, Medical, Mindfulness, Healthy Food, Self-care & Pamper, Home & Garden, Sleep, Longevity
Life Enrichment: Kids & Grandkids, Business & Finance, Skill Building, Pro. Development, Emerging Tech, Sustainability, Community Care, Pets, Friends
Entertainment: Football, Formula 1, Soccer, Tennis, Basketball, Baseball, Winter Sports, Golf, Music, Artist Fandom, Festivals & Concerts, Show Fandom, Movie Genres, Hollywood Culture, Actor Fandom, Animation & Fandom, Gambling, Video Games, Theme Parks
Arts & Culture: Museums, Historical Events, Politics & Current Events, Landmarks, Art & Photography, Theater, Architecture, Literature, Creation, STEM, Fashion, Beauty, Shopping, Local Culture, Painting & Drawing, Sculpting, Crafts, Classic Cars, Antiques & Vintage
""".strip()

# ---- Simple cache ----
class LruTtlCache:
    def __init__(self, capacity=1000, ttl_seconds=3600):
        self.capacity = capacity
        self.ttl = ttl_seconds
        self.store: OrderedDict[str, Tuple[float, Any]] = OrderedDict()
        self.lock = threading.Lock()
    def get(self, key):
        with self.lock:
            item = self.store.get(key)
            if not item:
                return None
            ts, val = item
            if (time.time() - ts) > self.ttl:
                self.store.pop(key, None)
                return None
            self.store.move_to_end(key)
            return val
    def set(self, key, val):
        with self.lock:
            self.store[key] = (time.time(), val)
            self.store.move_to_end(key)
            if len(self.store) > self.capacity:
                self.store.popitem(last=False)

def stable_cache_key(labels: List[Dict[str, Any]], top_k: int) -> str:
    sorted_labels = sorted(
        [(str(l.get("label")), float(l.get("score", 0.0))) for l in labels],
        key=lambda x: (-x[1], x[0])
    )[:15]
    payload = json.dumps({"labels": sorted_labels, "k": top_k}, separators=(",", ":"), sort_keys=True)
    import hashlib
    return hashlib.md5(payload.encode("utf-8")).hexdigest()

@dataclass
class MatcherConfig:
    api_key: str
    model_name: str = MODEL_NAME
    temperature: float = 0.1
    top_p: float = 0.8
    max_output_tokens: int = 1024
    cache_capacity: int = 2000
    cache_ttl_seconds: int = 3600
    max_retries: int = 4
    timeout_seconds: int = 20

def build_prompt(aws_labels: List[Dict[str, Any]], top_k: int) -> str:
    labels_text = "\n".join([f"- {l['label']} (confidence: {float(l['score']):.2f})" for l in aws_labels])

    return (
f"""You are a travel passion matching expert. Given AWS Rekognition labels from a user's photo, match them to relevant travel passions.

AVAILABLE_PASSIONS = {PASSIONS_TEXT}

DETECTED LABELS FROM PHOTO:
{labels_text}

HARD CONSTRAINTS (must follow):
1) Only output tags that appear verbatim in AVAILABLE_PASSIONS (leaf items only). Never invent/normalize tags (e.g., no "Night").
2) The "tag" MUST be one of the leaf passions (e.g., "Yoga"). The "category" MUST be its parent (e.g., "Wellness"). 
   Never output a parent category (Outdoor, Lifestyle, Culinary, Wellness, Life Enrichment, Entertainment, Arts & Culture) as the tag.
3) If no tag from AVAILABLE_PASSIONS fits, return empty arrays for both "matched" and "suggested".
4) Always return valid JSON exactly like:
{{
  "matched": [{{"tag":"Passion","category":"Category","score":0.90,"reasoning":"why"}}],
  "suggested": [{{"tag":"Passion","category":"Category","score":0.70,"reasoning":"why"}}]
}}
5) Scores: 0.0–1.0. ≥0.80 = strong, 0.60–0.79 = good, ≤0.59 = weak.
6) If at least one reasonable candidate exists, include ≥1 item in "matched". If evidence is weak, cap it at ≤0.59 and keep the reason short.
7) try to find realistic or near match and Return max {top_k} matches and max {top_k} suggestions and if no match found return Minimum of 1 match and 1 suggestion if available in pure JSON:

MATCHING RULES:
- Prioritize specific activities over generic categories.
- Consider context (e.g., "child + indoor + portrait" → Kids & Grandkids).
- Beach/water signals → Beach Retreat; only map to Water Sports/Surf/Kayak & Canoe if explicit gear/terms appear.
- Food/restaurant → Culinary passions.
- Architecture/landmarks → Arts & Culture (Landmarks, Architecture).
- Sports equipment → that sport (e.g., racket → Tennis).
- Family/baby → Life Enrichment (Kids & Grandkids).
- Amusement/ride cues (e.g., "Amusement Park", "Roller Coaster", "Ferris wheel", "Theme park", "Ride", "Coaster") → Entertainment: Theme Parks (top priority when present).


DISAMBIGUATION & NEGATIVE GUARDRAILS:
- Do NOT match Ski & Snowboard from generic "Snow/Mountain" alone. Require "Ski/Skiing/Snowboard" or visible gear. 
  Generic snow/mountain ≤0.59 to Mountains or Hike & Glamp if tent/backpack.
- Do NOT match Wildlife without an animal label (Deer/Bird/Bear/etc.). 
  Woods + Camera/Tripod = Art & Photography (or Hike if no camera).
- Prefer Hike & Glamp over Hike when Tent appears with Mountain/Backpack.
- A generic "Person" alone does not imply fitness.
- Festivals & Concerts: require at least one of "Fireworks", "Stage", "Concert", "Crowd", "Live performance", "Festival", "Confetti", "Spotlights". 
  If only fireworks/night sky, you MAY match Festivals & Concerts but cap ≤0.79 unless "Crowd/Stage" also present. 
  Never output "Night" as a tag (it’s not in AVAILABLE_PASSIONS).
- When amusement/ride cues are present, downrank generic "Nature/Outdoors" (≤0.49) and prefer Theme Parks as the primary match.
- "Advertisement/Sign/Billboard/Neon" does NOT map to a tag by itself. If urban context is implied, you MAY suggest "City Exploration" ≤0.59.
- Architecture: only match/suggest when explicit building/structure cues are present (e.g., "Building", "Skyscraper", "Facade", "Cathedral", "Temple", "Bridge", "House", "Castle"). Otherwise, do not output Architecture.
  

TIGHTENED RULES YOU MUST APPLY:
- Art & Photography: Only return or suggest when there is a clear association with art media/contexts 
  (e.g., "Painting", "Mosaic", "Tapestry", "Sculpture", "Museum", "Canvas", "Gallery", "Exhibition") 
  OR when there is camera intent (e.g., "Camera", "Tripod", "DSLR", "Mirrorless", "Photographer", "Taking photo", "Lens", "Phone camera", "Studio backdrop", "Light stand", "Selfie"). 
  If none, do NOT match; at most suggest with ≤0.59 for prominent lighting/backdrop cues.
- Architecture: Only return or suggest when a building/structure is clearly present (e.g., "Building", "Skyscraper", "Facade", "Cathedral", "Temple", "Bridge", "House", "Castle").
- Fish: Only return or suggest "Fish" if it is clearly about fishing as an activity (e.g., "Fishing rod", "Reel", "Bait", "Tackle box", "Fishing boat", "Angler", "Catching fish"). 
  A generic fish/animal label does NOT imply the "Fish" passion.
- Sand: If "Sand" appears without sport gear, it likely implies "Beach Retreat" (not Water Sports).
- Golf disambiguation: 
  If {{"Golf"}} AND any of {{"Crowd","Grandstand","Tournament","Scoreboard","Caddie","Audience"}} → prefer "Golf" with category = "Entertainment". 
  If golf gear/course cues only (e.g., "Club","Ball","Tee","Cart","Putting green","Fairway") and no crowd → "Golf" with category = "Lifestyle".

DIVERSITY & SUGGESTION POLICY
- Prefer diverse parents when multiple candidates are close; don’t return two near-duplicates under the same parent unless evidence is overwhelming.
- If there is at least one weak but reasonable secondary candidate from AVAILABLE_PASSIONS, include exactly 1 item in "suggested" (score ≤0.59). If none exists, leave "suggested" empty.

SCORING HINTS:
- Strong (≥0.80): explicit gear/action label(s) or multiple consistent cues.
- Good (0.60–0.79): clear but indirect cues.
- Weak (≤0.59): generic scenes without activity gear; use broad passions sparingly and keep reasons short.

MAPPING HINTS (examples, not exhaustive):
- "Fireworks" → Entertainment: Festivals & Concerts (stronger with Crowd/Stage; otherwise ≤0.79)
- "Landmark/Monument/Architecture" → Arts & Culture: Landmarks or Architecture
- "Restaurant/Food/Dish/Chef/Tableware" → Culinary (Restaurants, Local Cuisine, Fine Dining, etc.)
- "Ball/Racket/Net/Helmet/Jersey" → that specific sport in Entertainment/Lifestyle
- "Tent/Backpack/Trail" → Outdoor: Hike & Glamp / Hike
- "Camera/Tripod/Photographer" OR explicit art media → Arts & Culture: Art & Photography
- "Parade/Market/Traditional dress" → Arts & Culture: Local Culture
- "Amusement Park" / "Roller Coaster" / "Ferris wheel" → Entertainment: Theme Parks (≥0.85 if high confidence)
- "Advertisement/Sign/Billboard/Neon" + urban context → Lifestyle: City Exploration (≤0.59 suggestion)


OUTPUT:
Return valid JSON only, no extra narration.
""")
    
def parse_llm_response(text: str) -> Dict[str, Any]:
    text = text.strip()
    if text.startswith("```"):
        text = text.split("```")[1].strip()
        if text.startswith("json"):
            text = text[4:].strip()
    try:
        data = json.loads(text)
    except Exception:
        return {"matched": [], "suggested": [], "error": "Invalid JSON"}
    return data

class LLMPassionMatcher:
    def __init__(self, config: MatcherConfig):
        self.cfg = config
        self.cache = LruTtlCache(config.cache_capacity, config.cache_ttl_seconds)
        genai.configure(api_key=API_KEY)
        self.model = genai.GenerativeModel(config.model_name)
    def match(self, labels, top_k=6):
        key = stable_cache_key(labels, top_k)
        if cached := self.cache.get(key):
            return {"cached": True, **cached}
        prompt = build_prompt(labels, top_k)
        for attempt in range(self.cfg.max_retries):
            try:
                resp = self.model.generate_content(
                    prompt,
                    generation_config={
                        "temperature": self.cfg.temperature,
                        "top_p": self.cfg.top_p,
                        "max_output_tokens": self.cfg.max_output_tokens,
                        
                    },
                )
                result = parse_llm_response(resp.text)
                self.cache.set(key, result)
                return {"cached": False, **result}
            except Exception as e:
                print(f"Retry {attempt+1}: {e}")
                time.sleep(min(2**attempt, 8) + random.random())
        return {"matched": [], "suggested": [], "error": "LLM request failed"}

if __name__ == "__main__":
    cfg = MatcherConfig(api_key=API_KEY)
    matcher = LLMPassionMatcher(cfg)
    start_time = time.time()

    print("Running Passion Matcher test...\n")
    result = matcher.match(AWS_LABELS, TOP_K)
    print(json.dumps(result, indent=2))
    
    end_time = time.time()
    elapsed = end_time - start_time
    print(f"\nTime taken: {elapsed:.2f} seconds")

