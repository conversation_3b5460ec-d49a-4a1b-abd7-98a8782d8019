"""Authentication Service - Fora-Marriott Pseudo Microservice."""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.auth import router as auth_router
from app.core import get_settings, init_sentry, setup_logging

# Get settings
settings = get_settings()

# Setup logging and monitoring
setup_logging("auth")
init_sentry("auth")

# Create FastAPI application
app = FastAPI(
    title=f"{settings.app_name} - Auth Service",
    description="Authentication and authorization microservice",
    version=settings.version,
    debug=settings.debug,
    openapi_prefix="/auth",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Include auth routes
app.include_router(auth_router)
