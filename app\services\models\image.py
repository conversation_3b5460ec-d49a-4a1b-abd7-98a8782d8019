"""Upload models for DynamoDB."""

import uuid
from datetime import UTC, datetime
from typing import Any

from pydantic import BaseModel, Field


class ImageRef(BaseModel):
    """Reference to an uploaded image."""

    image_id: str
    original_name: str
    s3_key: str
    extension: str
    status: str = "pending"  # pending, uploaded, processing, ready, failed

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary."""
        return {
            "image_id": self.image_id,
            "original_name": self.original_name,
            "s3_key": self.s3_key,
            "extension": self.extension,
            "status": self.status,
        }


class UploadProgress(BaseModel):
    """Upload progress tracking."""

    processed: int = 0
    total: int = 0

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary."""
        return {
            "processed": self.processed,
            "total": self.total,
        }


class UploadBatch(BaseModel):
    """Upload batch model for DynamoDB."""

    user_id: str
    batch_id: str
    status: str = "pending"  # pending, processing, ready, failed
    num_files: int
    progress: UploadProgress
    created_at: str
    image_refs: list[ImageRef] = Field(default_factory=list)

    @property
    def pk(self) -> str:
        """Primary key for DynamoDB."""
        return f"USER#{self.user_id}"

    @property
    def sk(self) -> str:
        """Sort key for DynamoDB."""
        return f"BATCH#{self.batch_id}"

    def to_dynamodb_item(self) -> dict[str, Any]:
        """Convert to DynamoDB item format."""
        return {
            "PK": self.pk,
            "SK": self.sk,
            "batch_id": self.batch_id,
            "user_id": self.user_id,
            "status": self.status,
            "num_files": self.num_files,
            "progress": self.progress.to_dict(),
            "created_at": self.created_at,
            "image_refs": [ref.to_dict() for ref in self.image_refs],
        }

    @classmethod
    def from_dynamodb_item(cls, item: dict[str, Any]) -> "UploadBatch":
        """Create UploadBatch from DynamoDB item."""
        progress_data = item.get("progress", {"processed": 0, "total": 0})
        image_refs_data = item.get("image_refs", [])

        return cls(
            user_id=item["user_id"],
            batch_id=item["batch_id"],
            status=item.get("status", "pending"),
            num_files=item["num_files"],
            progress=UploadProgress(**progress_data),
            created_at=item["created_at"],
            image_refs=[ImageRef(**ref) for ref in image_refs_data],
        )

    @classmethod
    def create_new(cls, user_id: str, image_names: list[str]) -> "UploadBatch":
        """Create a new upload batch."""
        batch_id = str(uuid.uuid4())
        now = datetime.now(UTC).isoformat()

        # Generate image references
        image_refs = []
        for name in image_names:
            # Extract extension
            extension = name.split(".")[-1].lower() if "." in name else ""

            # Generate unique image ID with industry standard naming
            image_id = f"img_{uuid.uuid4().hex[:12]}"

            # Generate S3 key path
            s3_key = f"uploads/{user_id}/{batch_id}/{image_id}.{extension}"

            image_refs.append(
                ImageRef(
                    image_id=image_id,
                    original_name=name,
                    s3_key=s3_key,
                    extension=extension,
                    status="pending",
                )
            )

        return cls(
            user_id=user_id,
            batch_id=batch_id,
            status="pending",
            num_files=len(image_names),
            progress=UploadProgress(processed=0, total=len(image_names)),
            created_at=now,
            image_refs=image_refs,
        )


# Request/Response Models
class UploadInitRequest(BaseModel):
    """Request model for upload initialization."""

    image_names: list[str]


class UploadUrl(BaseModel):
    """Upload URL response model."""

    key: str
    url: str
    image_id: str
    original_name: str


class UploadInitResponse(BaseModel):
    """Response model for upload initialization."""

    batch_id: str
    upload_urls: list[UploadUrl]
