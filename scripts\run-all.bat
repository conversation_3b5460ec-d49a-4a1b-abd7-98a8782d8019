@echo off
echo Starting All Fora-Marriott Microservices in Windows Terminal tabs...
echo.

:: Open Auth Service in a new tab
wt -w 0 nt -d . cmd /k "scripts\run-auth.bat" ^
; nt -d . cmd /k "scripts\run-images.bat" ^
; nt -d . cmd /k "scripts\run-tags.bat" ^
; nt -d . cmd /k "scripts\run-admin.bat"

echo.
echo Services will be available at:
echo   Auth Service:   http://localhost:8010/docs
echo   Images Service: http://localhost:8011/docs
echo   Tags Service:   http://localhost:8012/docs
echo   Admin Service:  http://localhost:8013/docs
echo.
echo All services started in separate tabs of Windows Terminal!
pause
