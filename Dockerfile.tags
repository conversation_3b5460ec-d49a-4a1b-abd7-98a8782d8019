# =========================
# Stage 1: Builder
# =========================
FROM python:3.11-slim AS builder

# Environment
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

WORKDIR /app

# Install build dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv (fast dependency installer)
RUN pip install --no-cache-dir uv

# Copy dependency files
COPY pyproject.toml poetry.lock* ./

# Install dependencies into a temporary directory
RUN uv pip install --no-cache-dir --target=/app/deps .

# =========================
# Stage 2: Runtime
# =========================
FROM python:3.11-slim

# Environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PORT=8003

WORKDIR /app

# Install only minimal runtime dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy Python dependencies from builder stage
COPY --from=builder /app/deps /usr/local/lib/python3.11/site-packages

# Copy application source code
COPY . .

# Create non-root user for security
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose the service port
EXPOSE ${PORT}

# Run application with uvicorn
CMD ["python", "-m", "uvicorn", "app.main_tags:app", "--host", "0.0.0.0", "--port", "8003"]
