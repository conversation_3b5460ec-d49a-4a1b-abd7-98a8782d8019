@echo off
echo Installing uv package manager...
echo.

REM Install uv using PowerShell
powershell -Command "irm https://astral.sh/uv/install.ps1 | iex"

if %errorlevel% neq 0 (
    echo Failed to install uv automatically.
    echo Please install manually from: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

echo.
echo ✅ uv installed successfully!
echo.
echo Adding uv to PATH for this session...
set PATH=%USERPROFILE%\.local\bin;%PATH%

echo.
echo Testing uv installation...
uv --version

if %errorlevel% neq 0 (
    echo ❌ uv not found in PATH. Please restart your terminal or add to system PATH:
    echo %USERPROFILE%\.local\bin
    pause
    exit /b 1
)

echo.
echo ✅ uv is ready to use!
echo.
echo Next steps:
echo   1. Run: uv sync
echo   2. Run: scripts\run-auth.bat
echo.
pause
