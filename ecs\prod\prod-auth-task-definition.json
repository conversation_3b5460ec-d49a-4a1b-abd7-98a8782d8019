{"family": "fora-marriott-prod-auth-api-task-definition", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::161242380101:role/fora-marriott-prod-myEcsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "X86_64"}, "containerDefinitions": [{"name": "fora-marriott-dev-auth-api", "image": "161242380101.dkr.ecr.us-east-1.amazonaws.com/fora-marriott-prod:auth-api-prod-latest", "essential": true, "cpu": 1024, "memory": 2048, "portMappings": [{"appProtocol": "http", "containerPort": 8001, "hostPort": 8001, "protocol": "tcp"}, {"containerPort": 50051, "hostPort": 50051, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/auth-service-fora-marriott-prod", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 655350, "hardLimit": 655350}, {"name": "nproc", "softLimit": 65535, "hardLimit": 65535}], "environment": [{"name": "ENV", "value": "prod"}, {"name": "MAX_CONNECTIONS", "value": "50000"}]}], "volumes": []}