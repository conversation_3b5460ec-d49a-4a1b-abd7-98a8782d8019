"""Test health endpoints for all services."""

import pytest
from fastapi.testclient import TestClient

from app.main_admin import app as admin_app
from app.main_auth import app as auth_app
from app.main_images import app as images_app
from app.main_tags import app as tags_app


@pytest.fixture
def auth_client():
    """Create test client for auth service."""
    return TestClient(auth_app)


@pytest.fixture
def images_client():
    """Create test client for images service."""
    return TestClient(images_app)


@pytest.fixture
def tags_client():
    """Create test client for tags service."""
    return TestClient(tags_app)


@pytest.fixture
def admin_client():
    """Create test client for admin service."""
    return TestClient(admin_app)


def test_auth_health(auth_client):
    """Test auth service health endpoint."""
    response = auth_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["service"] == "auth"


def test_images_health(images_client):
    """Test images service health endpoint."""
    response = images_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["service"] == "images"


def test_tags_health(tags_client):
    """Test tags service health endpoint."""
    response = tags_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["service"] == "tags"


def test_admin_health(admin_client):
    """Test admin service health endpoint."""
    response = admin_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["service"] == "admin"


def test_auth_root(auth_client):
    """Test auth service root endpoint."""
    response = auth_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "Auth Service" in data["message"]


def test_images_root(images_client):
    """Test images service root endpoint."""
    response = images_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "Images Service" in data["message"]


def test_tags_root(tags_client):
    """Test tags service root endpoint."""
    response = tags_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "Tags Service" in data["message"]


def test_admin_root(admin_client):
    """Test admin service root endpoint."""
    response = admin_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "Admin Service" in data["message"]
