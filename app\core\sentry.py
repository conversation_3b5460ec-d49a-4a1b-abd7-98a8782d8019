"""Sentry integration for error tracking and monitoring."""

import logging
from typing import Any

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from .config import get_settings


def init_sentry(service_name: str) -> None:
    """Initialize Sentry for error tracking."""
    settings = get_settings()

    if not settings.sentry_enabled or not settings.sentry_dsn:
        logging.info("Sentry disabled or DSN not provided")
        return

    # Configure logging integration
    sentry_logging = LoggingIntegration(
        level=logging.INFO,  # Capture info and above as breadcrumbs
        event_level=logging.ERROR,  # Send errors as events
    )

    # Initialize Sentry
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        environment=settings.sentry_environment,
        traces_sample_rate=settings.sentry_traces_sample_rate,
        integrations=[
            FastApiIntegration(auto_enabling_integrations=False),
            sentry_logging,
        ],
        # Set service name as a tag
        before_send=lambda event, hint: add_service_context(event, service_name),
        # Release tracking (optional)
        release=f"{service_name}@{settings.version}",
    )

    logging.info(f"Sentry initialized for {service_name} service")


def add_service_context(event: dict[str, Any], service_name: str) -> dict[str, Any]:
    """Add service context to Sentry events."""
    event.setdefault("tags", {})["service"] = service_name
    event.setdefault("extra", {})["service_name"] = service_name
    return event


def capture_exception(
    error: Exception,
    extra_data: dict[str, Any] | None = None,
    tags: dict[str, str] | None = None,
) -> None:
    """Capture exception with additional context."""
    with sentry_sdk.push_scope() as scope:
        if extra_data:
            for key, value in extra_data.items():
                scope.set_extra(key, value)

        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)

        sentry_sdk.capture_exception(error)


def capture_message(
    message: str,
    level: str = "info",
    extra_data: dict[str, Any] | None = None,
    tags: dict[str, str] | None = None,
) -> None:
    """Capture custom message with additional context."""
    with sentry_sdk.push_scope() as scope:
        if extra_data:
            for key, value in extra_data.items():
                scope.set_extra(key, value)

        if tags:
            for key, value in tags.items():
                scope.set_tag(key, value)

        sentry_sdk.capture_message(message, level=level)


def set_user_context(user_id: str, email: str | None = None) -> None:
    """Set user context for Sentry events."""
    sentry_sdk.set_user(
        {
            "id": user_id,
            "email": email,
        }
    )


def add_breadcrumb(
    message: str,
    category: str = "custom",
    level: str = "info",
    data: dict[str, Any] | None = None,
) -> None:
    """Add breadcrumb for debugging context."""
    sentry_sdk.add_breadcrumb(
        message=message,
        category=category,
        level=level,
        data=data or {},
    )
