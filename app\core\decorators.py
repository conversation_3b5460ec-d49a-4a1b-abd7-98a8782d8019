"""Common decorators for API responses."""

import functools
from collections.abc import Callable
from typing import Any, TypeVar

from fastapi import HTTPException
from fastapi.responses import JSONResponse

from app.services.models.common import CommonResponse

F = TypeVar("F", bound=Callable[..., Any])


def response_handler(func: F) -> F:
    """Decorator to wrap API responses in CommonResponse format."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            # Call the original function
            result = await func(*args, **kwargs)

            # If result is already a Response object, return as-is
            if hasattr(result, "status_code"):
                return result

            # Check if the endpoint has a response_model that's not CommonResponse
            # If so, return the result directly to avoid double wrapping
            if hasattr(func, "__annotations__") and "return" in func.__annotations__:
                return_type = func.__annotations__["return"]
                # If it's a specific model (not dict), return directly
                if hasattr(return_type, "__name__") and return_type.__name__ != "dict":
                    return CommonResponse.success(result).model_dump()

            # Wrap successful result in CommonResponse for dict returns
            return CommonResponse.success(result).model_dump()

        except HTTPException as e:
            # Handle FastAPI HTTP exceptions
            return JSONResponse(
                status_code=e.status_code,
                content=CommonResponse.error(
                    error_msg=e.detail, status_code=e.status_code
                ).model_dump(),
            )
        except Exception as e:
            # Handle unexpected exceptions
            error_msg = str(e) if str(e) else "An unexpected error occurred"
            return JSONResponse(
                status_code=500,
                content=CommonResponse.error(
                    error_msg=error_msg, status_code=500
                ).model_dump(),
            )

    return wrapper


def auth_response_handler(func: F) -> F:
    """Decorator specifically for auth endpoints that don't use CommonResponse."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            # Call the original function and return as-is for auth endpoints
            return await func(*args, **kwargs)

        except HTTPException as e:
            # Re-raise HTTP exceptions for FastAPI to handle
            raise e
        except Exception as e:
            # Convert unexpected exceptions to HTTP exceptions
            raise HTTPException(status_code=500, detail=str(e))

    return wrapper
