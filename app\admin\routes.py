"""Admin service routes."""

from datetime import UTC, datetime

from fastapi import APIRouter

from app.core import get_settings

router = APIRouter()
settings = get_settings()


@router.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for admin service."""
    return {
        "status": "ok",
        "timestamp": datetime.now(UTC).isoformat(),
        "service": "admin",
        "version": settings.version,
        "environment": settings.environment,
    }


@router.get("/")
async def root() -> dict[str, str]:
    """Root endpoint for admin service."""
    return {
        "message": "Fora-Marriott Admin Service",
        "version": settings.version,
        "docs": "/docs",
        "health": "/health",
    }


# TODO: Add your admin endpoints here
# Example endpoints:
#
# @router.get("/admin/users")
# async def list_users(skip: int = 0, limit: int = 100):
#     """List all users with pagination."""
#     # Implementation here
#     pass
#
# @router.get("/admin/stats")
# async def get_statistics():
#     """Get system statistics and metrics."""
#     # Implementation here
#     pass
#
# @router.post("/admin/cleanup")
# async def cleanup_resources():
#     """Cleanup unused resources (images, tags, etc.)."""
#     # Implementation here
#     pass
#
# @router.get("/admin/health-all")
# async def check_all_services():
#     """Check health of all microservices."""
#     # Implementation here
#     pass
