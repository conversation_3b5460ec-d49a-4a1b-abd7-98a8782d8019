#!/bin/bash
echo "Fora-Marriott Microservice Setup Script (Linux/Ubuntu)"
echo "================================================="
echo

echo "Step 1: Installing uv package manager..."
./scripts/install-uv.sh
if [ $? -ne 0 ]; then
    echo "Failed to install uv."
    exit 1
fi

echo
echo "Step 2: Installing project dependencies..."
export PATH="$HOME/.local/bin:$PATH"
uv sync
if [ $? -ne 0 ]; then
    echo "Failed to install dependencies."
    exit 1
fi

echo
echo "Step 3: Making scripts executable..."
chmod +x scripts/*.sh

echo
echo "Step 4: Running tests to verify setup..."
uv run pytest tests/ -v
if [ $? -ne 0 ]; then
    echo "Tests failed. Please check the setup."
    exit 1
fi

echo
echo "✅ Setup completed successfully!"
echo
echo "Quick start commands:"
echo "  ./scripts/run-auth.sh     - Start Auth service"
echo "  ./scripts/run-all.sh      - Start all services"
echo "  uv run pytest            - Run tests"
echo "  uv run ruff check .       - Lint code"
echo
